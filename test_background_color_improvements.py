#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试背景色改进
验证文字背景色和编辑器背景色的一致性
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_robot_highlighter_background_colors():
    """测试RobotHighlighter的背景色设置"""
    print("=== 测试RobotHighlighter背景色设置 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QColor
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        from iplatform.highlight.RobotHighlighter import RobotHighlighter
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试不同主题下的背景色设置
        themes_to_test = [
            ('light', '#ffffff'),
            ('dark', '#353535'),
            ('dark_orange', '#3a3a3a')
        ]
        
        for theme, expected_bg in themes_to_test:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 切换主题
            theme_manager.set_theme(theme, save_preference=False)
            
            # 创建RobotHighlighter实例
            highlighter = RobotHighlighter(None)
            
            # 检查默认背景色
            if hasattr(highlighter, 'defaultPaper'):
                actual_bg = highlighter.defaultPaper().name().lower()
                bg_correct = actual_bg == expected_bg
                print(f"默认背景色: {actual_bg} (期望: {expected_bg}) {'✅' if bg_correct else '❌'}")
            else:
                print("❌ 不支持获取默认背景色")
            
            # 检查各个样式的背景色是否一致
            styles_to_check = [
                highlighter.Default,
                highlighter.Section,
                highlighter.Keyword,
                highlighter.Variable,
                highlighter.Comment,
                highlighter.TestCase,
                highlighter.Setting,
                highlighter.Number,
                highlighter.String,
                highlighter.BuiltinKeyword,
                highlighter.Tag,
                highlighter.LibraryKeyword
            ]
            
            all_consistent = True
            for style in styles_to_check:
                if hasattr(highlighter, 'paper'):
                    style_bg = highlighter.paper(style).name().lower()
                    if style_bg != expected_bg:
                        all_consistent = False
                        print(f"❌ 样式 {style} 背景色不一致: {style_bg}")
            
            if all_consistent:
                print("✅ 所有样式背景色一致")
        
        app.quit()
        
    except Exception as e:
        print(f"测试RobotHighlighter背景色时出错: {e}")
        import traceback
        traceback.print_exc()

def test_text_editor_background_colors():
    """测试TextEditor的背景色设置"""
    print("\n=== 测试TextEditor背景色设置 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QColor
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        from controller.system_plugin.text_edit.TextEditor import TextEditor
        
        # 创建TextEditor实例
        text_editor = TextEditor()
        
        # 测试深色主题
        print("\n--- 测试深色主题 ---")
        text_editor._set_theme("dark")
        
        # 检查编辑器背景色
        paper_color = text_editor.paper()
        expected_bg = "#353535"
        bg_correct = paper_color.name().lower() == expected_bg
        print(f"编辑器背景色: {paper_color.name()} (期望: {expected_bg}) {'✅' if bg_correct else '❌'}")
        
        # 检查调色板背景色
        palette = text_editor.palette()
        base_color = palette.color(palette.Base)
        palette_bg_correct = base_color.name().lower() == expected_bg
        print(f"调色板背景色: {base_color.name()} (期望: {expected_bg}) {'✅' if palette_bg_correct else '❌'}")
        
        # 测试浅色主题
        print("\n--- 测试浅色主题 ---")
        text_editor._set_theme("white")
        
        paper_color = text_editor.paper()
        expected_bg = "#ffffff"
        bg_correct = paper_color.name().lower() == expected_bg
        print(f"编辑器背景色: {paper_color.name()} (期望: {expected_bg}) {'✅' if bg_correct else '❌'}")
        
        app.quit()
        
    except Exception as e:
        print(f"测试TextEditor背景色时出错: {e}")
        import traceback
        traceback.print_exc()

def test_line_edit_area_background_colors():
    """测试LineEditArea的背景色设置"""
    print("\n=== 测试LineEditArea背景色设置 ===")
    
    try:
        # 模拟LineEditArea的背景色逻辑
        def get_background_color(theme, text):
            if theme == 'dark':
                return "#353535"  # 统一使用编辑器背景色
            elif theme == 'dark_orange':
                return "#3A3A3A"  # 统一使用编辑器背景色
            else:
                return "#ffffff" if text else "#c0c0c0"
        
        # 测试不同主题和内容组合
        test_cases = [
            ('dark', 'Log To Console', '#353535'),
            ('dark', '', '#353535'),
            ('dark_orange', 'Set Variable', '#3a3a3a'),
            ('dark_orange', '', '#3a3a3a'),
            ('light', 'Should Be Equal', '#ffffff'),
            ('light', '', '#c0c0c0'),
        ]
        
        for theme, text, expected_bg in test_cases:
            actual_bg = get_background_color(theme, text)
            bg_correct = actual_bg.lower() == expected_bg
            content_desc = f"'{text}'" if text else "空内容"
            print(f"{theme}主题 {content_desc}: {actual_bg} (期望: {expected_bg}) {'✅' if bg_correct else '❌'}")
        
    except Exception as e:
        print(f"测试LineEditArea背景色时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试背景色改进...")
    
    try:
        test_robot_highlighter_background_colors()
        test_text_editor_background_colors()
        test_line_edit_area_background_colors()
        
        print("\n=== 改进总结 ===")
        print("1. 去掉了文字背景色的亮度差异")
        print("2. 统一了语法高亮器的背景色设置")
        print("3. 调整了编辑器背景色，使其比主题色稍微浅一些")
        print("4. 确保了LineEditArea与编辑器背景色的一致性")
        
        print("\n=== 颜色方案 ===")
        print("- 深色主题: #353535 (比原来的#2B2B2B稍微浅)")
        print("- 深色橙色主题: #3A3A3A (比原来的#323232稍微浅)")
        print("- 浅色主题: #FFFFFF (保持不变)")
        
        print("\n=== 预期效果 ===")
        print("- 文字背景色与编辑器背景色完全一致")
        print("- 不再有亮色文字背景覆盖深色编辑器背景")
        print("- 编辑器背景色比主题色稍微浅一些，提供更好的视觉体验")
        print("- 所有语法高亮样式使用统一的背景色")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
