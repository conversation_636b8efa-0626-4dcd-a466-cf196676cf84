#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试LineEditArea文本位置稳定性修复
验证在浅色主题和深色橙色主题下，每次点击用例或关键字重新加载后，
Arguments、Timeout、Teardown、Return Value四个输入框的内容位置保持一致
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QLabel, QHBoxLayout, QTextEdit, QComboBox)
from PyQt5.QtCore import Qt, QTimer

def create_test_line_edit_areas():
    """创建测试的LineEditArea"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建四个LineEditArea实例
        arguments_area = LineEditArea(parent, 'Arguments')
        arguments_area.load()
        
        timeout_area = LineEditArea(parent, 'Timeout')
        timeout_area.load()
        
        teardown_area = LineEditArea(parent, 'Teardown')
        teardown_area.load()
        
        return_value_area = LineEditArea(parent, 'Return Value')
        return_value_area.load()
        
        return arguments_area, timeout_area, teardown_area, return_value_area
        
    except Exception as e:
        print(f"创建LineEditArea失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def switch_theme(theme_name):
    """切换主题"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        theme_manager = ThemeManager()
        result = theme_manager.set_theme(theme_name)
        print(f"切换到{theme_name}主题: {result}")
        return result
        
    except Exception as e:
        print(f"主题切换失败: {e}")
        return False

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("LineEditArea文本位置稳定性测试")
    main_window.resize(900, 700)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
LineEditArea文本位置稳定性修复测试

问题: 在浅色主题和深色橙色主题下，每点击一次用例或关键字，重新加载后，
编辑页签里Arguments、Timeout、Teardown、Return Value四个输入框里的内容位置会变化

修复内容:
1. 改进垂直居中逻辑，先重置文档边距再计算
2. 增加延迟执行机制，确保布局完成后再居中
3. 添加主题切换时的重新居中处理
4. 改进窗口大小改变时的居中稳定性

测试方法:
1. 选择不同主题进行切换
2. 多次点击"模拟重新加载数据"按钮
3. 观察输入框中文字位置是否保持一致
4. 调整窗口大小，观察文字位置是否稳定
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建主题选择器
    theme_layout = QHBoxLayout()
    theme_label = QLabel("选择主题:")
    theme_combo = QComboBox()
    theme_combo.addItems(["light", "dark", "dark_orange"])
    theme_layout.addWidget(theme_label)
    theme_layout.addWidget(theme_combo)
    theme_layout.addStretch()
    layout.addLayout(theme_layout)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    reload_data_btn = QPushButton("模拟重新加载数据")
    clear_data_btn = QPushButton("清空数据")
    test_stability_btn = QPushButton("稳定性测试(10次)")
    
    button_layout.addWidget(reload_data_btn)
    button_layout.addWidget(clear_data_btn)
    button_layout.addWidget(test_stability_btn)
    layout.addLayout(button_layout)
    
    # 创建LineEditArea实例
    arguments_area, timeout_area, teardown_area, return_value_area = create_test_line_edit_areas()
    
    if arguments_area:
        layout.addWidget(QLabel("测试输入框 - 观察文字位置是否稳定:"))
        layout.addLayout(arguments_area.get_layout())
        layout.addLayout(timeout_area.get_layout())
        layout.addLayout(teardown_area.get_layout())
        layout.addLayout(return_value_area.get_layout())
    else:
        layout.addWidget(QLabel("❌ 无法创建LineEditArea实例"))
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    output_text.setMaximumHeight(200)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 测试数据
    test_data = [
        ("${arg1} | ${arg2} | ${arg3}", "30s", "Close Browser", "${result}"),
        ("${username} | ${password}", "60s", "Teardown Suite", "${status}"),
        ("${url} | ${timeout}", "45s", "Close All Browsers", "${response}"),
        ("${data} | ${format}", "20s", "Clear Cache", "${output}"),
    ]
    current_data_index = 0
    
    # 连接主题切换事件
    def on_theme_changed():
        theme_name = theme_combo.currentText()
        output_text.append(f"切换到主题: {theme_name}")
        success = switch_theme(theme_name)
        if success:
            output_text.append(f"✅ 主题切换成功")
        else:
            output_text.append(f"❌ 主题切换失败")
    
    theme_combo.currentTextChanged.connect(on_theme_changed)
    
    # 连接按钮事件
    def reload_data():
        nonlocal current_data_index
        if arguments_area:
            try:
                # 使用不同的测试数据模拟重新加载
                data = test_data[current_data_index % len(test_data)]
                current_data_index += 1
                
                output_text.append(f"--- 第{current_data_index}次重新加载数据 ---")
                
                # 模拟重新加载过程
                arguments_area.fill_data(data[0])
                timeout_area.fill_data(data[1])
                teardown_area.fill_data(data[2])
                return_value_area.fill_data(data[3])
                
                output_text.append(f"✅ 数据重新加载完成")
                output_text.append("请观察输入框中文字位置是否保持一致")
            except Exception as e:
                output_text.append(f"❌ 重新加载数据失败: {e}")
        else:
            output_text.append("❌ LineEditArea实例不可用")
    
    def clear_data():
        if arguments_area:
            try:
                arguments_area.fill_data("")
                timeout_area.fill_data("")
                teardown_area.fill_data("")
                return_value_area.fill_data("")
                
                output_text.append("✅ 数据已清空")
            except Exception as e:
                output_text.append(f"❌ 清空数据失败: {e}")
        else:
            output_text.append("❌ LineEditArea实例不可用")
    
    def test_stability():
        """进行稳定性测试"""
        output_text.clear()
        output_text.append("=== 开始稳定性测试 ===")
        output_text.append("将进行10次数据重新加载，观察文字位置是否稳定")
        
        def do_test_round(round_num):
            if round_num <= 10:
                output_text.append(f"第{round_num}轮测试...")
                reload_data()
                # 延迟进行下一轮测试
                QTimer.singleShot(1000, lambda: do_test_round(round_num + 1))
            else:
                output_text.append("=== 稳定性测试完成 ===")
                output_text.append("请检查:")
                output_text.append("1. 每次重新加载后文字位置是否一致")
                output_text.append("2. 不同主题下文字是否都正确居中")
                output_text.append("3. 窗口大小改变时文字是否保持居中")
        
        do_test_round(1)
    
    reload_data_btn.clicked.connect(reload_data)
    clear_data_btn.clicked.connect(clear_data)
    test_stability_btn.clicked.connect(test_stability)
    
    # 自动初始化
    def auto_init():
        output_text.append("=== 自动初始化测试 ===")
        # 先切换到浅色主题
        theme_combo.setCurrentText("light")
        QTimer.singleShot(500, lambda: reload_data())
    
    QTimer.singleShot(1000, auto_init)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 LineEditArea文本位置稳定性测试...")
    print("=" * 60)
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
