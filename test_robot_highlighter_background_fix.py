#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试RobotHighlighter背景色修复
验证语法高亮器是否正确设置背景色
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

def test_robot_highlighter_themes():
    """测试RobotHighlighter的主题设置"""
    print("=== 测试RobotHighlighter主题设置 ===")
    
    try:
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        from iplatform.highlight.RobotHighlighter import RobotHighlighter
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试不同主题下的颜色设置
        themes_to_test = ['light', 'dark', 'dark_orange']
        
        for theme in themes_to_test:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 切换主题
            theme_manager.set_theme(theme, save_preference=False)
            current_theme = theme_manager.get_current_theme()
            print(f"当前主题: {current_theme}")
            
            # 创建RobotHighlighter实例
            highlighter = RobotHighlighter(None)
            
            # 检查默认背景色和文字颜色
            if hasattr(highlighter, 'defaultPaper'):
                default_paper = highlighter.defaultPaper()
                print(f"默认背景色: {default_paper.name()}")
            else:
                print("不支持获取默认背景色")
            
            if hasattr(highlighter, 'defaultColor'):
                default_color = highlighter.defaultColor()
                print(f"默认文字颜色: {default_color.name()}")
            else:
                print("不支持获取默认文字颜色")
            
            # 验证背景色是否正确
            expected_backgrounds = {
                'light': '#ffffff',
                'dark': '#2b2b2b',
                'dark_orange': '#323232'
            }
            
            expected_bg = expected_backgrounds.get(theme, '#ffffff')
            
            if hasattr(highlighter, 'defaultPaper'):
                actual_bg = highlighter.defaultPaper().name().lower()
                bg_correct = actual_bg == expected_bg
                print(f"背景色正确: {'✅' if bg_correct else '❌'} (期望: {expected_bg}, 实际: {actual_bg})")
            else:
                print("无法验证背景色")
            
            # 测试主题刷新功能
            if hasattr(highlighter, 'refresh_theme'):
                print("✅ 支持主题刷新功能")
                highlighter.refresh_theme()
                print("✅ 主题刷新完成")
            else:
                print("❌ 不支持主题刷新功能")
        
        app.quit()
        
    except Exception as e:
        print(f"测试RobotHighlighter时出错: {e}")
        import traceback
        traceback.print_exc()

def test_theme_color_methods():
    """测试主题颜色方法"""
    print("\n=== 测试主题颜色方法 ===")
    
    try:
        from iplatform.highlight.RobotHighlighter import RobotHighlighter
        
        # 创建RobotHighlighter实例
        highlighter = RobotHighlighter(None)
        
        # 测试各个主题颜色方法是否存在
        methods_to_test = [
            '_apply_theme_colors',
            '_apply_light_theme_colors',
            '_apply_dark_theme_colors',
            '_apply_dark_orange_theme_colors',
            '_set_font_styles',
            'refresh_theme'
        ]
        
        for method_name in methods_to_test:
            has_method = hasattr(highlighter, method_name)
            print(f"{method_name}: {'✅' if has_method else '❌'}")
            
            if has_method and method_name.startswith('_apply_') and method_name.endswith('_colors'):
                try:
                    method = getattr(highlighter, method_name)
                    method()
                    print(f"  - {method_name} 执行成功")
                except Exception as e:
                    print(f"  - {method_name} 执行失败: {e}")
        
    except Exception as e:
        print(f"测试主题颜色方法时出错: {e}")
        import traceback
        traceback.print_exc()

def test_text_editor_integration():
    """测试TextEditor集成"""
    print("\n=== 测试TextEditor集成 ===")
    
    try:
        from controller.system_plugin.text_edit.TextEditor import TextEditor
        from iplatform.highlight.RobotHighlighter import RobotHighlighter
        
        # 创建TextEditor实例
        text_editor = TextEditor()
        
        # 设置Robot语法高亮器
        robot_highlighter = RobotHighlighter(text_editor)
        text_editor.setLexer(robot_highlighter)
        
        print("✅ Robot语法高亮器设置完成")
        
        # 测试主题应用方法
        theme_methods = [
            '_apply_dark_syntax_highlighting',
            '_apply_light_syntax_highlighting',
            '_apply_robot_dark_colors',
            '_apply_robot_light_colors'
        ]
        
        for method_name in theme_methods:
            has_method = hasattr(text_editor, method_name)
            print(f"{method_name}: {'✅' if has_method else '❌'}")
        
        # 测试主题切换
        print("\n--- 测试主题切换 ---")
        
        # 应用深色主题
        if hasattr(text_editor, '_apply_dark_syntax_highlighting'):
            text_editor._apply_dark_syntax_highlighting()
            print("✅ 深色主题应用完成")
        
        # 应用浅色主题
        if hasattr(text_editor, '_apply_light_syntax_highlighting'):
            text_editor._apply_light_syntax_highlighting()
            print("✅ 浅色主题应用完成")
        
    except Exception as e:
        print(f"测试TextEditor集成时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试RobotHighlighter背景色修复...")
    
    try:
        test_robot_highlighter_themes()
        test_theme_color_methods()
        test_text_editor_integration()
        
        print("\n=== 修复总结 ===")
        print("1. 在RobotHighlighter中添加了主题感知功能")
        print("2. 根据当前主题设置正确的背景色和文字颜色")
        print("3. 支持浅色、深色和深色橙色三种主题")
        print("4. 添加了refresh_theme()方法支持主题切换")
        print("5. 修改了TextEditor以使用新的主题刷新机制")
        
        print("\n=== 预期效果 ===")
        print("- 浅色主题: 白色背景 (#FFFFFF)")
        print("- 深色主题: 深灰色背景 (#2B2B2B)")
        print("- 深色橙色主题: 深灰色背景 (#323232)")
        print("- 语法高亮颜色根据主题自动调整")
        print("- 主题切换时背景色正确更新")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
