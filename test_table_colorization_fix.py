#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专门测试表格文字染色修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor

def test_colorization_settings():
    """测试ColorizationSettings的颜色获取"""
    print("=== 测试ColorizationSettings ===")
    
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 获取当前主题
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        print(f"当前主题: {current_theme}")
        
        # 创建ColorizationSettings实例
        colorization = ColorizationSettings()
        
        # 测试颜色获取
        print(f"关键字颜色: {colorization.get_keyword_color()}")
        print(f"变量颜色: {colorization.get_variable_color()}")
        print(f"文字颜色: {colorization.get_text_color()}")
        print(f"背景颜色: {colorization.get_background_color()}")
        print(f"注释颜色: {colorization.get_comment_color()}")
        print(f"wei注释颜色: {colorization.get_wei_comment_color()}")
        
        # 切换到深色橙色主题
        if current_theme != 'dark_orange':
            print("\n切换到深色橙色主题...")
            theme_manager.set_theme('dark_orange')
            
            print(f"切换后关键字颜色: {colorization.get_keyword_color()}")
            print(f"切换后变量颜色: {colorization.get_variable_color()}")
            print(f"切换后文字颜色: {colorization.get_text_color()}")
            print(f"切换后背景颜色: {colorization.get_background_color()}")
            print(f"切换后注释颜色: {colorization.get_comment_color()}")
            
            # 切换回原主题
            theme_manager.set_theme(current_theme)
            print(f"已切换回原主题: {current_theme}")
        
        return True
        
    except Exception as e:
        print(f"ColorizationSettings测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_colorizer_functionality():
    """测试Colorizer的染色功能"""
    print("\n=== 测试Colorizer染色功能 ===")
    
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
        from controller.system_plugin.edit.view.component.table.Table import Table
        from PyQt5.QtWidgets import QWidget
        
        # 创建测试表格
        parent = QWidget()
        table = Table(parent)
        table.load()
        
        # 创建Colorizer实例
        colorizer = Colorizer()
        
        # 测试数据
        test_lines = [
            ['Log', 'Hello World', '', '', '# 这是注释'],
            ['${variable}', '=', 'Set Variable', 'test_value', ''],
            ['FOR', '${item}', 'IN', '@{list}', ''],
            ['    Log', '${item}', '', '', ''],
            ['END', '', '', '', ''],
            ['Sleep', '1s', '', '', '# 等待1秒'],
            ['Should Be Equal', '${variable}', 'test_value', '', ''],
            ['Comment', '这是一个注释行', '', '', '']
        ]
        
        print("开始测试染色...")
        for row, line in enumerate(test_lines):
            print(f"染色第{row}行: {line}")
            colorizer.colorize(table, row, line)
        
        print("Colorizer染色测试完成")
        return True
        
    except Exception as e:
        print(f"Colorizer测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_table():
    """创建测试表格"""
    table = QTableWidget(10, 6)
    table.setHorizontalHeaderLabels(['关键字', '参数1', '参数2', '参数3', '参数4', '注释'])
    
    # RobotFramework测试数据
    test_data = [
        ['Log', 'Hello World', '', '', '', '# 输出日志'],
        ['${variable}', '=', 'Set Variable', 'test_value', '', ''],
        ['FOR', '${item}', 'IN', '@{list}', '', ''],
        ['    Log', '${item}', '', '', '', ''],
        ['END', '', '', '', '', ''],
        ['Sleep', '1s', '', '', '', '# 等待1秒'],
        ['Should Be Equal', '${variable}', 'test_value', '', '', ''],
        ['${result}', '=', 'Get Text', 'xpath=//div', '', ''],
        ['Comment', '这是一个注释行', '', '', '', ''],
        ['[Arguments]', '${arg1}', '${arg2}', '', '', '']
    ]
    
    for row, row_data in enumerate(test_data):
        for col, cell_data in enumerate(row_data):
            item = QTableWidgetItem(cell_data)
            table.setItem(row, col, item)
    
    return table

def apply_colorizer_to_table(table):
    """使用Colorizer对表格进行染色"""
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
        
        colorizer = Colorizer()
        colorizer.init_settings()
        
        print("开始使用Colorizer染色表格...")
        
        for row in range(table.rowCount()):
            line = []
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    line.append(item.text())
                else:
                    line.append('')
            
            if any(line):  # 只染色有内容的行
                print(f"染色第{row}行: {line}")
                # 创建一个模拟的Table对象
                class MockTable:
                    def __init__(self, qt_table):
                        self._table = qt_table
                
                mock_table = MockTable(table)
                colorizer.colorize(mock_table, row, line)
        
        print("Colorizer染色完成")
        
    except Exception as e:
        print(f"Colorizer染色失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("表格文字染色修复测试")
    main_window.resize(1000, 700)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
表格文字染色修复测试

问题: 深色橙色主题下编辑页签的表格文字染色未生效

修复内容:
1. 修复ColorizationSettings的主题颜色获取
2. 修复Colorizer的颜色缓存机制
3. 改进关键字识别逻辑

测试方法:
1. 点击"测试颜色设置"查看ColorizationSettings是否正常
2. 点击"应用Colorizer染色"测试Colorizer功能
3. 点击"切换主题"测试主题切换时的颜色变化
4. 观察表格中关键字、变量、注释等是否有不同颜色
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    test_settings_btn = QPushButton("测试颜色设置")
    test_colorizer_btn = QPushButton("测试Colorizer功能")
    apply_colorizer_btn = QPushButton("应用Colorizer染色")
    switch_theme_btn = QPushButton("切换主题")
    
    button_layout.addWidget(test_settings_btn)
    button_layout.addWidget(test_colorizer_btn)
    button_layout.addWidget(apply_colorizer_btn)
    button_layout.addWidget(switch_theme_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_test_table()
    layout.addWidget(QLabel("测试表格 - 观察文字颜色:"))
    layout.addWidget(table)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    output_text.setMaximumHeight(200)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 连接按钮事件
    def test_settings():
        output_text.clear()
        result = test_colorization_settings()
        output_text.append(f"颜色设置测试: {'成功' if result else '失败'}")
    
    def test_colorizer():
        output_text.clear()
        result = test_colorizer_functionality()
        output_text.append(f"Colorizer功能测试: {'成功' if result else '失败'}")
    
    def apply_colorizer():
        output_text.clear()
        apply_colorizer_to_table(table)
        output_text.append("Colorizer染色已应用到表格")
    
    def switch_theme():
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current = theme_manager.get_current_theme()
            new_theme = 'dark_orange' if current != 'dark_orange' else 'light'
            if theme_manager.set_theme(new_theme):
                output_text.append(f"主题已切换: {current} -> {new_theme}")
                # 重新应用染色
                apply_colorizer_to_table(table)
            else:
                output_text.append("主题切换失败")
        except Exception as e:
            output_text.append(f"主题切换失败: {e}")
    
    test_settings_btn.clicked.connect(test_settings)
    test_colorizer_btn.clicked.connect(test_colorizer)
    apply_colorizer_btn.clicked.connect(apply_colorizer)
    switch_theme_btn.clicked.connect(switch_theme)
    
    # 自动运行测试
    def run_auto_tests():
        output_text.append("=== 自动测试开始 ===")
        test_colorization_settings()
        test_colorizer_functionality()
        apply_colorizer_to_table(table)
        output_text.append("=== 自动测试完成 ===")
    
    QTimer.singleShot(1000, run_auto_tests)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 表格文字染色修复测试...")
    print("=" * 50)
    
    # 运行控制台测试
    test_colorization_settings()
    test_colorizer_functionality()
    
    print("\n" + "=" * 50)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
