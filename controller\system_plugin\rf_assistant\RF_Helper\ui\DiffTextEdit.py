# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import difflib


class DiffTextEdit(QTextBrowser):
    def __init__(self):
        super().__init__()

    def set_colored_diff(self, text1, text2):
        diff = difflib.ndiff(text1.splitlines(keepends=True), text2.splitlines(keepends=True))
        for line in diff:
            fmt = QTextCharFormat()
            if line.startswith('-'):
                fmt.setBackground(QColor('pink'))
                self.append_colored_line(line, fmt)
            elif line.startswith('+'):
                fmt.setBackground(QColor('lightgreen'))
                self.append_colored_line(line, fmt)
            elif line.startswith('?'):
                continue
            else:
                fmt.setBackground(QColor('white'))
                self.append_colored_line(line, fmt)

    def set_colored_text(self, texts):
        for lines in texts:
            fmt = QTextCharFormat()
            line = lines.strip()
            if line.startswith('Comment'):
                fmt.setBackground(QColor('pink'))
                self.append_colored_line(line, fmt, True)
            elif line.startswith('log'):
                fmt.setBackground(QColor('#87CEFA'))
                self.append_colored_line(line, fmt, True)
            else:
                fmt.setBackground(QColor('white'))
                self.append_colored_line(line, fmt, True)

    def move_cursor_to_keyword(self, keyword):
        """将光标移动到第一个高亮的关键字位置，并滚动到该位置。"""
        cursor = self.textCursor()
        document_length = self.document().toPlainText().find(keyword)
        # 找到关键字在文档中的位置
        if document_length != -1:
            # 移动光标到关键字开始的位置
            cursor.setPosition(document_length)
            # 设置文本光标位置，以便自动滚动到关键字
            self.setTextCursor(cursor)
            self.ensureCursorVisible()

    def set_colored_text_test_case(self, texts):
        for line in texts:
            fmt = QTextCharFormat()
            if line.startswith('RDC路径') or line.startswith('脚本路径'):
                fmt.setForeground(QColor('black'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('*'):
                fmt.setForeground(QColor('green'))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith(' ') or line.startswith('\t'):
                fmt.setForeground(QColor('black'))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith('Variables') or line.startswith('Resource') or line.startswith('Suite'):
                fmt.setForeground(QColor('black'))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith('用例脚本'):
                fmt.setForeground(QColor('purple'))
                self.append_colored_line(line, fmt, True)
                continue
            else:
                fmt.setForeground(QColor('blue'))
                self.append_colored_line(line, fmt, True)

    def set_colored_text_rdc_step(self, texts):
        for line in texts:
            fmt = QTextCharFormat()
            if line.startswith('操作步骤'):
                fmt.setForeground(QColor('blue'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('预期结果'):
                fmt.setForeground(QColor('green'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('用例名称'):
                fmt.setForeground(QColor('purple'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('预置条件'):
                fmt.setForeground(QColor('brown'))
                self.append_colored_line(line, fmt, True)
                continue
            else:
                fmt.setForeground(QColor('black'))
                self.append_colored_line(line, fmt, True)

    def append_colored_line(self, line, fmt, isChangeLine=False):
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(line, fmt)
        if isChangeLine:
            cursor.insertBlock()
            


