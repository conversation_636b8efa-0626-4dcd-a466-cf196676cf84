#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试菜单重构和深色橙色主题表格修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout, QMenuBar, QMenu, QAction)
from PyQt5.QtCore import Qt

def create_test_table():
    """创建测试表格"""
    table = QTableWidget(5, 5)
    table.setHorizontalHeaderLabels(['列1', '列2', '列3', '列4', '列5'])
    
    # 填充一些测试数据
    for row in range(5):
        for col in range(5):
            item = QTableWidgetItem(f"单元格({row},{col})")
            table.setItem(row, col, item)
    
    return table

def apply_dark_orange_theme_styles(main_window, table):
    """应用深色橙色主题样式"""
    # 读取深色橙色主题CSS
    try:
        with open("resources/qss/dark_orange.qss", "r", encoding='utf-8') as f:
            style = f.read()
        main_window.setStyleSheet(style)
        print("已应用深色橙色主题样式")
    except Exception as e:
        print(f"应用深色橙色主题失败: {e}")
        # 手动应用表格样式
        table_style = """
            QTableWidget {
                background-color: #323232;
                color: #b1b1b1;
                gridline-color: #555555;
                border: 1px solid #555555;
            }
            QTableWidget::item {
                background-color: #323232;
                color: #b1b1b1;
                border: 1px solid #555555;
                padding: 2px;
            }
            QTableWidget::item:selected {
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ffa02f, stop: 1 #d7801a);
                color: #000000;
            }
            QHeaderView::section {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #616161, stop: 0.5 #505050, stop: 0.6 #434343, stop:1 #656565);
                color: #b1b1b1;
                padding-left: 4px;
                border: 1px solid #6c6c6c;
            }
        """
        table.setStyleSheet(table_style)
        main_window.setStyleSheet("""
            QMainWindow { background-color: #323232; color: #b1b1b1; }
            QWidget { background-color: #323232; color: #b1b1b1; }
            QPushButton { 
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #565656, stop: 0.1 #525252, stop: 0.5 #4e4e4e, stop: 0.9 #4a4a4a, stop: 1 #464646);
                color: #b1b1b1; 
                border: 1px solid #1e1e1e; 
                padding: 6px 12px;
                border-radius: 6px;
            }
            QPushButton:hover { 
                border: 2px solid qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ffa02f, stop: 1 #d7801a);
            }
            QLabel { color: #b1b1b1; }
        """)

def create_test_menu_bar(main_window):
    """创建测试菜单栏"""
    menu_bar = main_window.menuBar()
    
    # 文件菜单
    file_menu = menu_bar.addMenu('&文件')
    file_menu.addAction('新建项目')
    file_menu.addAction('打开项目')
    file_menu.addSeparator()
    file_menu.addAction('退出')
    
    # 工具菜单
    tools_menu = menu_bar.addMenu('&工具')
    tools_menu.addAction('搜索关键字')
    tools_menu.addAction('搜索测试用例')
    
    # 插件菜单
    plugin_menu = menu_bar.addMenu('&插件')
    plugin_menu.addAction('插件管理')
    
    # 设置菜单（新增）
    settings_menu = menu_bar.addMenu('&设置')
    settings_menu.addAction('偏好设置')
    
    # 帮助菜单（替代简介菜单）
    help_menu = menu_bar.addMenu('&帮助')
    help_menu.addAction('关于RFCode')
    help_menu.addAction('产品说明')
    
    return menu_bar

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("菜单重构和深色橙色主题表格测试")
    main_window.resize(1000, 700)
    
    # 创建菜单栏
    menu_bar = create_test_menu_bar(main_window)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试菜单重构和深色橙色主题下表格单元格样式")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    theme_btn = QPushButton("应用深色橙色主题")
    reset_btn = QPushButton("重置样式")
    button_layout.addWidget(theme_btn)
    button_layout.addWidget(reset_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_test_table()
    layout.addWidget(QLabel("测试表格 - 观察单元格背景色是否统一:"))
    layout.addWidget(table)
    
    # 添加菜单说明
    menu_info = QLabel("""
菜单结构测试:
1. 文件菜单 - 保持不变
2. 工具菜单 - 移除了"偏好设置"
3. 插件菜单 - 保持不变
4. 设置菜单 - 新增，包含"偏好设置"
5. 帮助菜单 - 替代"简介"菜单，包含"关于RFCode"和"产品说明"
    """)
    menu_info.setWordWrap(True)
    layout.addWidget(menu_info)
    
    def apply_theme():
        """应用深色橙色主题"""
        apply_dark_orange_theme_styles(main_window, table)
        theme_btn.setText("已应用深色橙色主题")
        print("深色橙色主题已应用")
        print("请检查:")
        print("1. 表格单元格背景色是否统一为深色 (#323232)")
        print("2. 表格文字颜色是否为浅色 (#b1b1b1)")
        print("3. 选中单元格是否为橙色渐变")
        print("4. 表头是否为深色渐变")
    
    def reset_style():
        """重置样式"""
        main_window.setStyleSheet("")
        table.setStyleSheet("")
        theme_btn.setText("应用深色橙色主题")
        print("样式已重置")
    
    # 连接信号
    theme_btn.clicked.connect(apply_theme)
    reset_btn.clicked.connect(reset_style)
    
    print("测试窗口已创建")
    print("请测试:")
    print("1. 点击菜单查看菜单结构是否正确")
    print("2. 点击'应用深色橙色主题'按钮测试表格样式")
    print("3. 观察表格单元格背景色是否统一")
    print("4. 检查菜单栏是否包含'设置'和'帮助'菜单")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
