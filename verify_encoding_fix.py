#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证编码修复 - 检查MainFrame.py中的编码修复是否正确
"""

import os
import sys

def check_mainframe_encoding_fix():
    """检查MainFrame.py中的编码修复"""
    mainframe_file = "view/MainFrame.py"
    
    if not os.path.exists(mainframe_file):
        print(f"❌ MainFrame.py文件不存在: {mainframe_file}")
        return False
    
    with open(mainframe_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含修复后的代码
    if 'with codecs.open(style_file, "r", encoding=\'utf-8\') as f:' in content:
        print("✅ MainFrame.py中的编码修复已正确应用")
        print("   - codecs.open()现在使用UTF-8编码")
        return True
    elif 'with codecs.open(style_file, "r") as f:' in content:
        print("❌ MainFrame.py中仍然存在编码问题")
        print("   - codecs.open()没有指定编码，可能导致UnicodeDecodeError")
        return False
    else:
        print("❓ MainFrame.py中没有找到相关的codecs.open()调用")
        return False

def check_theme_manager_encoding():
    """检查ThemeManager.py中的编码设置"""
    theme_manager_file = "controller/system_plugin/style/ThemeManager.py"
    
    if not os.path.exists(theme_manager_file):
        print(f"❌ ThemeManager.py文件不存在: {theme_manager_file}")
        return False
    
    with open(theme_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否正确使用UTF-8编码
    if 'with codecs.open(style_file, "r", encoding=\'utf-8\') as f:' in content:
        print("✅ ThemeManager.py中正确使用UTF-8编码")
        return True
    else:
        print("❌ ThemeManager.py中编码设置有问题")
        return False

def main():
    print("🔍 验证编码修复...")
    print("=" * 50)
    
    results = []
    
    print("\n1. 检查MainFrame.py编码修复:")
    results.append(check_mainframe_encoding_fix())
    
    print("\n2. 检查ThemeManager.py编码设置:")
    results.append(check_theme_manager_encoding())
    
    print("\n" + "=" * 50)
    
    if all(results):
        print("🎉 编码修复验证通过！")
        print("\n修复总结:")
        print("✅ MainFrame.py中的_set_global_style()方法已修复")
        print("✅ codecs.open()现在正确使用UTF-8编码")
        print("✅ 不会再出现UnicodeDecodeError错误")
        print("\n现在应用启动时应该不会再出现编码错误！")
        return True
    else:
        print("❌ 编码修复验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
