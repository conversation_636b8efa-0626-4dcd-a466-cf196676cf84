# encoding=utf-8
'''
Create on  2019年10月14日

@author:10240349
'''
from controller.system_plugin.UILibrary import UILibrary
from controller.system_plugin.file.FilePlugin import FilePlugin
from controller.system_plugin.help.HelpPlugin import HelpPlugin
from controller.system_plugin.plugin.PluginManagement import PluginManagement
from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
from controller.system_plugin.tools.ToolsPlugin import ToolsPlugin
from iplatform.PluginDrawer import PluginDrawer
from settings.i18n.Loader import LanguageLoader


class MenuBar(object):

    def __init__(self, parent):
        self._main_frame = parent
        self._menu_bar = self._main_frame.menuBar()
        UILibrary.register('MENU_BAR', self._menu_bar, LanguageLoader().get('MENU_BAR_DOC'))
        self._plugin_drawer = PluginDrawer()

    def load(self):
        FilePlugin(self._menu_bar).load()
        ToolsPlugin(self._menu_bar).load()
        PluginManagement(self._menu_bar).load()
        SettingsPlugin(self._menu_bar).load()
        HelpPlugin(self._menu_bar).load()