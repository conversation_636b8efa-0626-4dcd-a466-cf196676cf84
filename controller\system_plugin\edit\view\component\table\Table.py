# coding=utf-8
'''
Created on 2019年11月5日

@author: 10247557
'''

import logging
import traceback

from PyQt5.Qt import Qt, QMenu, QCursor, QItemSelectionModel
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import QTableWidget, QApplication, QVBoxLayout, QToolTip

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
from controller.system_plugin.edit.view.component.table.KeywordDescription import KeywordDescription
from controller.system_plugin.edit.view.component.table.TableEditArea import TableEditArea
from controller.system_plugin.edit.view.component.table.TableItem import TableItem
from controller.system_plugin.edit.view.component.table.TableMenu import TableMenu
from model.CurrentItem import CurrentItem
from model.data_file.Repository import BuildInKeyWordRepository, \
    LocalKeyWordRepository
from settings.SystemSettings import SystemSettings
from utility import DataHandler
from utility.log.SystemLogger import logger
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper


ENTER = 16777220
CTRL = 16777249


class _table(QTableWidget):
    #     focus_out_event = pyqtSignal()

    def __init__(self, parent):
        super(_table, self).__init__(parent)
        self.horizontalHeader().setDefaultSectionSize(SystemSettings().get_value('HORIZONTALHEADERWIDTH'))

    def mousePressEvent(self, event):
        self._specified_keyword_jumper = SpecifiedKeywordJumper()
        super().mousePressEvent(event)
        if event.button() == Qt.LeftButton:
            if self.currentItem() and self.currentItem().text():
                self._show_tool_tips(self.currentItem(), self._get_tips())
            if self._is_jumping():
                self._jump_to_location()

    def _get_tips(self):
        _list = self._get_row_content()
        _vals = []
        for val in _list:
            if LocalKeyWordRepository().query(val) and _list.index(val) < (len(_list) - 1):
                arguments = KeywordDescription().get_keyword_arguments(val)
                val_list = _list[_list.index(val) + 1:]
                content = self.currentItem().text().strip()
                if content and content in val_list:
                    index = val_list.index(content)
                    if index < len(arguments):
                        return arguments[index]

    def _get_row_content(self):
        all_col = self.columnCount()
        row_num = self.row(self.currentItem())
        _list = []
        for i in range(all_col):
            if self.item(row_num, i):
                _list.append(self.item(row_num, i).text().strip())
        return _list

    def _is_jumping(self):
        return QApplication.keyboardModifiers() == Qt.ControlModifier

    def _jump_to_location(self):
        keyword_name = self.currentItem().text().strip()
        keyword_path = self._specified_keyword_jumper.get_keyword_path_from_local_repository(keyword_name)
        self._specified_keyword_jumper.get_keyword_item(keyword_path, keyword_name)

    def _show_tool_tips(self, component, tip):
        QToolTip.setFont(QFont(SystemSettings().get_value('FONT'),
                               SystemSettings().get_value('TIP_SIZE')))
        if tip:
            component.setToolTip('Arguments:' + tip)

    def keyPressEvent(self, event):
        super().keyPressEvent(event)
        if event.key() == ENTER:
            SignalDistributor().modify_table_event()
        if event.key() == CTRL and self.currentItem():
            keyword_name = self.currentItem().text().strip()
            x = QCursor.pos().x()
            y = QCursor.pos().y()
            if self._is_keyword(keyword_name):
                KeywordDescription().open_dialog(keyword_name, x, y)

    def _is_keyword(self, name):
        des = BuildInKeyWordRepository().query(name)
        if not des:
            des = LocalKeyWordRepository().query(name)
        if des:
            return True
        else:
            return False

    def keyReleaseEvent(self, event):
        if event.key() == CTRL:
            KeywordDescription().close_dialog()


class Table(object):

    def __init__(self, parent):
        self._parent = parent
        Colorizer().init_settings()
        self._content = None
        self._is_show = False
        self._current_row = 0
        self._current_clomn = 0

    def get_layout(self):
        return self._layout

    def load(self):
        self._set_table()
        self._set_table_edit_area()
        self._set_layout()

    def _set_layout(self):
        self._layout = QVBoxLayout()
        self._layout.addWidget(self._table_edit_area)
        self._layout.addWidget(self._table)

    def _set_table(self):
        self._table = _table(self._parent)
        self._table.setItemDelegate(TableItem())
        self._table.setColumnCount(SystemSettings().get_value('ROW_NUM'))
        self._table.setRowCount(SystemSettings().get_value('COLUMN_NUM'))
        self.table_menu = TableMenu(self)
        self.table_menu.load_shortcut()
        self._table.setContextMenuPolicy(Qt.CustomContextMenu)
        self._set_table_action()
        self._set_table_header()

        # 连接主题变化信号
        self._connect_theme_signal()

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        print(f"表格主题切换到: {theme_id}")

        # 重新设置表头样式
        self._set_table_header()

        # 重新染色所有行
        self._recolorize_all_rows()

    def _recolorize_all_rows(self):
        """重新染色所有行"""
        try:
            all_row = self._table.rowCount()
            self._table.blockSignals(True)

            print(f"开始重新染色 {all_row} 行")

            for row in range(all_row):
                line = self._get_line_data(row)
                if line:  # 只染色有内容的行
                    Colorizer().colorize(self, row, line)

            print("表格重新染色完成")

        except Exception as e:
            print(f"重新染色失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._table.blockSignals(False)

    def _set_table_edit_area(self):
        self._table_edit_area = TableEditArea(self).get_edit()

    def change_show_flag(self, flag):
        self._is_show = flag

    def set_content(self, content):
        self._content = content

    def _set_table_action(self):
        self._table.currentItemChanged.connect(self._auto_colorize)
        self._table.itemSelectionChanged.connect(self._auto_colorize_current_row)
        self._table.itemChanged.connect(self._change_item)
        SignalDistributor().modify_table.connect(self._modify_data)
        SignalDistributor().format_save_item.connect(self._save_data)
        self._table.clicked.connect(self.table_menu.save_history_data)
        self._table.customContextMenuRequested.connect(self.generate_menu)

    def _change_item(self):
        self._auto_extension()
        self._modify_data()

    def _set_table_header(self):
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()

        self._table.verticalHeader().setFixedWidth(30)

        # 根据主题设置表头样式
        if theme_manager.is_dark_theme():
            header_style = "QHeaderView::section{border:0px; background-color:#3C3C3C; color:#E0E0E0;}"
            # 设置corner button样式
            corner_style = """
                QTableCornerButton::section {
                    background-color: #3C3C3C;
                    border: 1px solid #555555;
                }
            """
        else:
            header_style = "QHeaderView::section{border:0px groove gray; background-color:groove gray; color:#000000;}"
            # 浅色主题的corner button样式
            corner_style = """
                QTableCornerButton::section {
                    background-color: #F5F5F5;
                    border: 1px solid #D0D0D0;
                }
            """

        self._table.horizontalHeader().setStyleSheet(header_style)
        self._table.verticalHeader().setStyleSheet(header_style)
        self._table.verticalHeader().setDefaultAlignment(Qt.AlignCenter)

        # 设置corner button样式
        # 方法1: 直接在表格上设置corner button样式
        current_style = self._table.styleSheet()
        # 清除之前的corner button样式
        import re
        current_style = re.sub(r'QTableCornerButton::section\s*\{[^}]*\}', '', current_style)
        self._table.setStyleSheet(current_style + corner_style)

        # 方法2: 尝试通过findChild找到corner button
        try:
            from PyQt5.QtWidgets import QPushButton
            corner_button = self._table.findChild(QPushButton)
            if corner_button:
                if theme_manager.is_dark_theme():
                    corner_button.setStyleSheet("background-color: #3C3C3C; border: 1px solid #555555;")
                else:
                    corner_button.setStyleSheet("background-color: #F5F5F5; border: 1px solid #D0D0D0;")
        except Exception as e:
            print(f"设置corner button样式失败: {e}")

        # 方法3: 设置表格的corner button属性
        try:
            if hasattr(self._table, 'setCornerButtonEnabled'):
                self._table.setCornerButtonEnabled(True)
        except Exception:
            pass

    def reset_table(self):
        self._table.clear()
        self._table.setColumnCount(SystemSettings().get_value('ROW_NUM'))
        self._table.setRowCount(SystemSettings().get_value('COLUMN_NUM'))
        self._set_column_width()
        self._set_row_height()

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._line.setVisible(bool_value)
        self._edit_btn.setVisible(bool_value)
        self._clear_btn.setVisible(bool_value)

    def fill_data(self, parsed_item, isSaveAction=False):
        try:
            self._get_selected_item_location()
            self._parsed_item = parsed_item
            self.change_show_flag(True)
            self.reset_table()
            row, _ = -1, -1
            body_list = self._delete_empty_list(self._parsed_item.table)
            self.set_content(body_list)
            self._table.blockSignals(True)
            for line in body_list:
                row = row + 1
                Colorizer().colorize(self, row, line)
            self._table.blockSignals(False)
            self.change_show_flag(False)
            self.table_menu.save_history_data()
            if isSaveAction:
                self._focus_selected_row(len(body_list), self._table.rowCount())
        except Exception:
            logging.error('============table except=========')
            traceback.print_exc()

    def _get_selected_item_location(self):
        try:
            self._current_row = self._table.selectedIndexes()[0].row()
            self._current_clomn = self._table.selectedIndexes()[0].column()
        except:
            pass

    def _focus_selected_row(self, start, end):
        self._table.setCurrentCell(self._current_row, self._current_clomn, QItemSelectionModel.Select)
        self._table.verticalScrollBar().setSliderPosition(self._current_row)
        self._current_row = 0
        self._current_clomn = 0

    def _delete_empty_list(self, _list):
        new_list = []
        if not _list:
            return new_list
        for li in _list:
            if ''.join(li) != '':
                new_list.append(li)
        return new_list

    def generate_menu(self, pos):
        self._menu = QMenu()
        self.table_menu.load_menu()
        self._menu.exec_(self._table.mapToGlobal(pos))

    def _auto_extension(self):
        select_value = self._table.selectedItems()
        if select_value:
            value = select_value[0].text()
            row = self._table.selectedIndexes()[0].row()
            column = self._table.selectedIndexes()[0].column()
            all_row = self._table.rowCount()
            all_column = self._table.columnCount()
            if value.strip():
                if row + 1 == all_row:
                    self._table.insertRow(all_row)
                if column + 1 == all_column:
                    self._table.insertColumn(all_column)

    def _auto_colorize(self):
        result = self._get_data_with_empty_line()
        if result == self._content:
            return
        all_row = self._table.rowCount()
        try:
            self._table.blockSignals(True)
            for row in range(all_row):
                line = self._get_line_data(row)
                Colorizer().colorize(self, row, line)
        except Exception:
            traceback.print_exc()
        finally:
            self._table.blockSignals(False)

    def _auto_colorize_current_row(self):
        self._table.blockSignals(True)
        try:
            select_value = self._table.selectedItems()
            if select_value:
                row = self._table.selectedIndexes()[0].row()
                line = self._get_line_data(row, True)
                Colorizer().colorize(self, row, line)
        except Exception as e:
            logger.warn(e)
        finally:
            self._table.blockSignals(False)

    def colorize_rows(self, row_list):
        self._table.blockSignals(True)
        for row in row_list:
            line = self._get_line_data(row)
            Colorizer().colorize(self, row, line)
        self._table.blockSignals(False)

    def _get_line_data(self, current_row, contain_empty=False):
        all_column = self._table.columnCount()
        line = []
        has_value = False
        for column in range(all_column, -1, -1):
            item = self._table.item(current_row, column)
            if item and contain_empty:
                has_value = True
                text = item.text()
                if text.strip() == '':
                    text = text.strip()
                line.append(DataHandler.replace_blank_spaces(text))
            elif item and item.text().strip():
                has_value = True
                line.append(DataHandler.replace_blank_spaces(item.text()))
            elif has_value:
                line.append('')
        line.reverse()
        return line

    def get_data(self):
        self._delete_empty_line()
        result, has_value = [], False
        all_row = self._table.rowCount()
        all_column = self._table.columnCount()
        for row in range(all_row):
            line = []
            for column in range(all_column, -1, -1):
                if self._table.item(row, column):
                    has_value = True
                    line.append(self._table.item(row, column).text())
                elif has_value:
                    line.append('')
            line.reverse()
            if line:
                result.append(line)
            has_value = False
        return result

    def _delete_empty_line(self):
        self._get_empty_line()
        self._empty_row.reverse()
        if not self._empty_row:
            return
        all_row = self._table.rowCount() - 1
        start_delete = False
        for row in self._empty_row:
            if row == all_row:
                continue
            if not start_delete:
                if self._empty_row[self._empty_row.index(row) - 1] - row == 1:
                    continue
                else:
                    self._table.removeRow(row)
                    start_delete = True
            else:
                self._table.removeRow(row)

    def _get_empty_line(self):
        has_value = False
        self._empty_row = []
        all_row = self._table.rowCount()
        all_column = self._table.columnCount()
        for row in range(all_row):
            for column in range(all_column):
                if self._table.item(row, column) and self._table.item(row, column).text().strip():
                    has_value = True
            if not has_value:
                self._empty_row.append(row)
            has_value = False

    def _set_row_height(self):
        for i in range(self._table.rowCount()):
            self._table.setRowHeight(i, SystemSettings().get_value('VERTICALTALHEADERHEIGHT'))

    def _set_column_width(self):
        for i in range(self._table.columnCount()):
            self._table.setColumnWidth(i, SystemSettings().get_value('HORIZONTALHEADERWIDTH'))

    def _modify_data(self):
        result = self._get_data_with_empty_line()
        if not self._is_show and result != self._content:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify('body', result)
        self.set_content(result)

    def _refill_current_row(self):
        self._parsed_item.requery()
        body_list = self._parsed_item.table
        if self._table.selectedIndexes():
            row = self._table.selectedIndexes()[0].row()
            if row < len(body_list):
                line = body_list[row]
                Colorizer().colorize(self, row, line)

    def _save_data(self):
        self._parsed_item.requery()
        self.fill_data(self._parsed_item, isSaveAction=True)

    def _get_data_with_empty_line(self):
        rows = self._table.rowCount()
        result, has_value = [], False
        for row in range(rows, -1, -1):
            line = self._get_line_data(row)
            if line:
                has_value = True
                result.append(line)
            elif has_value:
                if line:
                    result.append(line)
                else:
                    result.append([''])
        result.reverse()
        return result
