# encoding=utf-8
'''
Create on  2019年10月14日

@author:10240349
'''
import os
import re

from PyQt5.Qt import QSettings
from settings.SystemConfig import *
from utility.Singleton import Singleton


SYSTEM_ROOT_DIR = "{}/..".format(os.path.abspath(os.path.dirname(__file__)))
CONFIG_DIR = "{}/config.ini".format(SYSTEM_ROOT_DIR)


@Singleton
class SystemSettings(object):

    def get_value(self, key):
        """
        获取指定键的值。

        该方法从系统配置文件中读取指定键的值，并返回该值。如果键不存在或无法解析，则返回None。

        参数:
        - key (str): 要获取值的键名。

        返回:
        - 返回键对应的值，如果键不存在或无法解析，则返回None。
        """
        try:
            # 首先尝试从SystemConfig获取常量
            from settings import SystemConfig
            return getattr(SystemConfig, key, None)
        except (AttributeError, ImportError):
            # 如果失败则尝试从config.ini读取
            settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
            return settings.value(key)

    def set(self, key, value):
        config_file = "{}/SystemConfig.py".format(os.path.abspath(os.path.dirname(__file__)))
        with open(config_file, 'r+', encoding='UTF-8') as f:
            lines = f.readlines()
        has_key = False
        for i in range(len(lines)):
            if re.match(r"{} = .*\n".format(key), lines[i]):
                lines[i] = '{} = "{}"\n'.format(key, value)
                has_key = True
        if not has_key:
            lines.append('{} = "{}"\n'.format(key, value))
        with open(config_file, 'w+', encoding='UTF-8') as f:
            for line in lines:
                f.write(line)
        print(config_file)

    @staticmethod
    def read(key):
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        return settings.value(key)

    @staticmethod
    def write(key, value):
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        return settings.setValue(key, value)

if __name__ == "__main__":
    ss = SystemSettings()
    print(ss.get_value("WARN"))
