# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *


class SetupDialogWindow(QDialog):
    setupDialogReady = pyqtSignal(dict)

    def __init__(self, x, y, para=None):
        super().__init__()
        self.setWindowTitle("设置")
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setGeometry(x, y, 300, 180)
        self.para = para
        print(para)
        self.setup_icon = QApplication.style().standardIcon(QStyle.SP_DialogSaveButton)
        self.setWindowIcon(self.setup_icon)

        self.setStyleSheet("""
            QDialog {
                background-color: #f9f9f9;
                border-radius: 10px;
            }
            QLabel {
                font-size: 14px;
                color: #333;
            }
            QComboBox {
                font-size: 14px;
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: white;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QGridLayout {
                margin: 20px;  /* 增加页边距 */
            }
        """)
        self.projectDic = self.para.get('projectDic')
        self.initUI(para)

    def initUI(self, para):
        # 项目标签及组合框
        self.projectLabel = QLabel(' 领域：')
        self.projectComboBox = QComboBox()
        self.projectComboBox.addItems(self.projectDic.values())
        self.projectComboBox.setCurrentText(self.projectDic.get(para.get('project', ''), ''))

        # 按钮
        self.okButton = QPushButton("确定")
        self.cancelButton = QPushButton("取消")
        self.okButton.clicked.connect(self.on_ok_button_clicked)
        self.cancelButton.clicked.connect(self.on_cancel_button_clicked)

        # 布局
        layout = QGridLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(15)  # 增加控件之间的间距
        layout.addWidget(self.projectLabel, 0, 0)
        layout.addWidget(self.projectComboBox, 0, 1, 1, 2)
        layout.addWidget(self.okButton, 1, 1)
        layout.addWidget(self.cancelButton, 1, 2)

        self.setLayout(layout)

    def on_ok_button_clicked(self):
        project = self.projectComboBox.currentText()
        with open('rfhelper_config.ini', 'w') as f:
            f.write(str({'project': project}))
        self.setupDialogReady.emit({'project': project})
        self.accept()

    def on_cancel_button_clicked(self):
        self.reject()

    def on_querykeywordDirButton_clicked(self):
        self.select_directory("选择目录", self.userkeywordDirEdit)

    def on_queryTestDirButton_clicked(self):
        self.select_directory("选择目录", self.testcaseDirEdit)

    def select_directory(self, title, edit):
        options = QFileDialog.Options()
        directory = QFileDialog.getExistingDirectory(self, title, options=options)
        if directory:
            print("选择的目录:", directory)
            edit.setText(directory)
