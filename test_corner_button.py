#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试表格corner button样式修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel)
from PyQt5.QtCore import Qt

def create_test_table():
    """创建测试表格"""
    table = QTableWidget(8, 5)
    table.setHorizontalHeaderLabels(['Step', 'Keyword', 'Argument1', 'Argument2', 'Comment'])
    
    # 添加一些测试数据
    test_data = [
        ['', 'Log', 'Hello World', '', '# 测试注释'],
        ['', 'Set Variable', '${var}', 'test value', ''],
        ['', 'Should Be Equal', '${var}', 'test value', ''],
        ['', 'Sleep', '1s', '', '# 等待1秒'],
        ['', 'Log', 'Test completed', '', ''],
    ]
    
    for row, row_data in enumerate(test_data):
        for col, cell_data in enumerate(row_data):
            item = QTableWidgetItem(cell_data)
            table.setItem(row, col, item)
    
    return table

def apply_dark_corner_style(table):
    """手动应用深色corner button样式"""
    corner_style = """
        QTableWidget {
            background-color: #404040;
            color: #E0E0E0;
            gridline-color: #555555;
        }
        QHeaderView::section {
            background-color: #3C3C3C;
            color: #E0E0E0;
            border: 1px solid #555555;
        }
        QTableCornerButton::section {
            background-color: #3C3C3C;
            border: 1px solid #555555;
        }
    """
    table.setStyleSheet(corner_style)

def apply_light_corner_style(table):
    """应用浅色corner button样式"""
    table.setStyleSheet("")

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("Corner Button样式测试")
    main_window.resize(900, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试表格左上角corner button的深色主题样式")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建主题切换按钮
    theme_btn = QPushButton("切换到深色主题")
    layout.addWidget(theme_btn)
    
    # 创建测试表格
    table = create_test_table()
    layout.addWidget(table)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("切换到深色主题")
                apply_light_corner_style(table)
                is_dark = False
                print("已切换到浅色主题")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("切换到浅色主题")
                apply_dark_corner_style(table)
                is_dark = True
                print("已切换到深色主题")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                main_window.setStyleSheet("")
                apply_light_corner_style(table)
                theme_btn.setText("切换到深色主题")
                is_dark = False
                print("手动切换到浅色主题")
            else:
                # 手动应用深色样式
                dark_style = """
                QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                QPushButton { 
                    background-color: #404040; 
                    color: #E0E0E0; 
                    border: 1px solid #555555; 
                    padding: 6px 12px;
                    border-radius: 4px;
                }
                QPushButton:hover { 
                    background-color: #4A90E2; 
                    border-color: #357ABD;
                }
                QLabel { color: #E0E0E0; }
                """
                main_window.setStyleSheet(dark_style)
                apply_dark_corner_style(table)
                theme_btn.setText("切换到浅色主题")
                is_dark = True
                print("手动切换到深色主题")
    
    theme_btn.clicked.connect(toggle_theme)
    
    print("测试窗口已创建")
    print("请点击按钮切换主题，特别观察表格左上角corner button的颜色变化")
    print("在深色主题下，corner button应该显示为深色背景")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
