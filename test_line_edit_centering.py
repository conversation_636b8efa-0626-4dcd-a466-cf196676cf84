#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试LineEditArea文本垂直居中修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QLabel, QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt, QTimer

def create_test_line_edit_areas():
    """创建测试的LineEditArea"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建四个LineEditArea实例
        arguments_area = LineEditArea(parent, 'Arguments')
        arguments_area.load()
        
        timeout_area = LineEditArea(parent, 'Timeout')
        timeout_area.load()
        
        teardown_area = LineEditArea(parent, 'Teardown')
        teardown_area.load()
        
        return_value_area = LineEditArea(parent, 'Return Value')
        return_value_area.load()
        
        return arguments_area, timeout_area, teardown_area, return_value_area
        
    except Exception as e:
        print(f"创建LineEditArea失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def test_theme_switching():
    """测试主题切换"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        print(f"当前主题: {current_theme}")
        
        # 切换到深色主题
        if current_theme != 'dark':
            print("切换到深色主题...")
            result = theme_manager.set_theme('dark')
            print(f"切换结果: {result}")
            
            new_theme = theme_manager.get_current_theme()
            print(f"切换后主题: {new_theme}")
            
            return 'dark'
        else:
            print("已经是深色主题")
            return current_theme
            
    except Exception as e:
        print(f"主题切换失败: {e}")
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("LineEditArea文本垂直居中修复测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
LineEditArea文本垂直居中修复测试

问题: 深色主题下，编辑页签里Arguments、Timeout、Teardown、Return Value四个输入框的内容默认未居中，导致文字显示不完整

修复内容:
1. 设置QTextEdit的垂直和水平滚动条为隐藏
2. 添加文本垂直居中方法
3. 在填充数据时自动居中文本
4. 在窗口大小改变时重新居中文本

测试方法:
1. 点击"切换到深色主题"
2. 点击"填充测试数据"
3. 观察输入框中的文字是否垂直居中显示
4. 尝试调整窗口大小，观察文字是否保持居中
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    switch_theme_btn = QPushButton("切换到深色主题")
    fill_data_btn = QPushButton("填充测试数据")
    clear_data_btn = QPushButton("清空数据")
    test_all_btn = QPushButton("运行完整测试")
    
    button_layout.addWidget(switch_theme_btn)
    button_layout.addWidget(fill_data_btn)
    button_layout.addWidget(clear_data_btn)
    button_layout.addWidget(test_all_btn)
    layout.addLayout(button_layout)
    
    # 创建LineEditArea实例
    arguments_area, timeout_area, teardown_area, return_value_area = create_test_line_edit_areas()
    
    if arguments_area:
        layout.addWidget(QLabel("测试输入框 - 观察文字是否垂直居中:"))
        layout.addLayout(arguments_area.get_layout())
        layout.addLayout(timeout_area.get_layout())
        layout.addLayout(teardown_area.get_layout())
        layout.addLayout(return_value_area.get_layout())
    else:
        layout.addWidget(QLabel("❌ 无法创建LineEditArea实例"))
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    output_text.setMaximumHeight(150)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 连接按钮事件
    def switch_theme():
        output_text.clear()
        theme = test_theme_switching()
        if theme:
            output_text.append(f"主题切换完成: {theme}")
        else:
            output_text.append("主题切换失败")
    
    def fill_data():
        output_text.clear()
        if arguments_area:
            try:
                # 填充测试数据
                arguments_area.fill_data("${arg1} | ${arg2} | ${arg3}")
                timeout_area.fill_data("30s")
                teardown_area.fill_data("Close Browser")
                return_value_area.fill_data("${result}")
                
                output_text.append("✅ 测试数据已填充")
                output_text.append("请观察输入框中的文字是否垂直居中显示")
            except Exception as e:
                output_text.append(f"❌ 填充数据失败: {e}")
        else:
            output_text.append("❌ LineEditArea实例不可用")
    
    def clear_data():
        output_text.clear()
        if arguments_area:
            try:
                arguments_area.fill_data("")
                timeout_area.fill_data("")
                teardown_area.fill_data("")
                return_value_area.fill_data("")
                
                output_text.append("✅ 数据已清空")
            except Exception as e:
                output_text.append(f"❌ 清空数据失败: {e}")
        else:
            output_text.append("❌ LineEditArea实例不可用")
    
    def test_all():
        output_text.clear()
        output_text.append("=== 开始完整测试 ===")
        
        # 1. 切换主题
        theme = test_theme_switching()
        if theme:
            output_text.append(f"✅ 主题切换成功: {theme}")
        else:
            output_text.append("❌ 主题切换失败")
            return
        
        # 2. 填充数据
        if arguments_area:
            try:
                arguments_area.fill_data("${arg1} | ${arg2} | ${arg3}")
                timeout_area.fill_data("30s")
                teardown_area.fill_data("Close Browser")
                return_value_area.fill_data("${result}")
                output_text.append("✅ 测试数据填充成功")
            except Exception as e:
                output_text.append(f"❌ 填充数据失败: {e}")
                return
        else:
            output_text.append("❌ LineEditArea实例不可用")
            return
        
        output_text.append("✅ 完整测试完成")
        output_text.append("")
        output_text.append("请检查:")
        output_text.append("1. 输入框中的文字是否垂直居中显示")
        output_text.append("2. 调整窗口大小时文字是否保持居中")
        output_text.append("3. 深色主题下背景色是否正确")
    
    switch_theme_btn.clicked.connect(switch_theme)
    fill_data_btn.clicked.connect(fill_data)
    clear_data_btn.clicked.connect(clear_data)
    test_all_btn.clicked.connect(test_all)
    
    # 自动运行测试
    def auto_test():
        output_text.append("=== 自动测试开始 ===")
        test_all()
    
    QTimer.singleShot(1000, auto_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 LineEditArea文本垂直居中修复测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_theme_switching()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
