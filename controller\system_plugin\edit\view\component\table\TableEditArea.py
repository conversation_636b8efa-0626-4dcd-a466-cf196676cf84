# coding=utf-8
'''
Created on 2019年12月10日

@author: 10247557
'''
from _functools import partial

from PyQt5.Qt import QTableWidgetItem
from PyQt5.QtWidgets import QTextEdit

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
from utility.log.SystemLogger import logger


# from controller.system_plugin.SignalDistributor import SignalDistributor
class TableEditArea():

    def __init__(self, table_obj):
        self._table_obj = table_obj
        self._table = table_obj._table
        self._set_table_edit_area()
        self._add_action()
        self._line_edit_flag = True

    def get_edit(self):
        return self._table_edit_area

    def _set_table_edit_area(self):
        self._table_edit_area = QTextEdit()
        self._table_edit_area.setFixedHeight(40)
        self._table_edit_area.setFontPointSize(12)
        self._set_edit_area_style()
        self._table_edit_area.textChanged.connect(self._fill_table_item)

        # 连接主题变化信号
        self._connect_theme_signal()

    def _set_edit_area_style(self):
        """设置编辑区域样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()

            if current_theme == 'dark':
                # 深色主题：使用更深的背景色，与主题一致但略微浅一些
                style = """
                    QTextEdit {
                        border: none;
                        background-color: #353535;
                        color: #E0E0E0;
                        selection-background-color: #4A90E2;
                        selection-color: #FFFFFF;
                    }
                """
            elif current_theme == 'dark_orange':
                # 深色橙色主题：使用与主题一致的深色背景
                style = """
                    QTextEdit {
                        border: none;
                        background-color: #3A3A3A;
                        color: #b1b1b1;
                        selection-background-color: QLinearGradient( x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ffa02f, stop: 1 #d7801a);
                        selection-color: #000000;
                    }
                """
            else:
                style = "border: none;background-color: #ececec;color: #000000;"

            self._table_edit_area.setStyleSheet(style)
        except Exception as e:
            print(f"设置表格编辑区域样式失败: {e}")
            self._table_edit_area.setStyleSheet("border: none;background-color: #ececec;")

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接表格编辑区域主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._set_edit_area_style()

    def _add_action(self):
        self._table.clicked.connect(partial(self._fill_table_edit_area, self))
        self._table.currentItemChanged.connect(partial(self._fill_table_edit_area, self))
        SignalDistributor().modify_table_item.connect(self._fill_table_edit_area_by_table_item)

    @staticmethod
    def _fill_table_edit_area(self):
        select_value = self._table.selectedItems()
        if select_value:
            row = self._table.selectedIndexes()[0].row()
            column = self._table.selectedIndexes()[0].column()
            item = self._table.item(row, column)
            if item:
                text = item.text()
                self._table_edit_area.setPlainText(text)
        else:
            self._table_edit_area.setPlainText(None)

    def _fill_table_edit_area_by_table_item(self, text):
        self._line_edit_flag = False
        self._table_edit_area.setPlainText(text)
        self._line_edit_flag = True

    def _get_data(self):
        text = self._table_edit_area.toPlainText()
        return text.replace('\n', '')

    def _fill_table_item(self):
        if self._line_edit_flag:
            table_edit_area_text = self._get_data()
            table_item_text = ''
            try:
                row = self._table.selectedIndexes()[0].row()
                column = self._table.selectedIndexes()[0].column()
                item = self._table.item(row, column)
                if item:
                    table_item_text = item.text()
                if table_item_text != table_edit_area_text:
                    item = QTableWidgetItem(table_edit_area_text)
                    self._table.setItem(int(row), int(column), item)
            except Exception as e:
                logger.warn(e)
