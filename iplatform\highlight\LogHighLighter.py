# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     LogHighLighter
   Description :
   Author :       10140129
   date：          2019/10/24
-------------------------------------------------
   Change Activity:
                   2019/10/24:
-------------------------------------------------
"""

from PyQt5.QtCore import QRegExp
from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QColor, QFont, QCursor
from PyQt5.Qt import Qt


class LogHighLighter(QSyntaxHighlighter):

    def __init__(self, parent=None):
        self._parent = parent
        document = self._parent.document()
        document.setMaximumBlockCount(10000)
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>ighter, self).__init__(document)
        self._rules = []
        self._formats = {}
        self._initialize_formats()
        self._set_rules()
        self._triple_single_re = QRegExp(r"""'''(?!")""")
        self._triple_double_re = QRegExp(r'''"""(?!')''')

    def _set_rules(self):
        CONSTANTS = ["False", "True", "None", "NotImplemented",
                     "Ellipsis"]
        self._rules.append((QRegExp(r"\bFAIL\b"), "fail"))
        self._rules.append((QRegExp(r"\bERROR\b"), "error"))
#         self._rules.append((QRegExp(r"\bERROR\b"), "error"))
        self._rules.append((QRegExp("|".join([r"\b%s\b" % constant 
                                              for constant in CONSTANTS])), "constant"))
        self._rules.append((QRegExp(r"\b[+-]?[0-9]+[lL]?\b"
                                    r"|\b[+-]?0[xX][0-9A-Fa-f]+[lL]?\b"
                                    r"|\b[+-]?[0-9]+(?:\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\b"),
                            "number"))
        self._rules.append((QRegExp(r"\bPyQt4\b|\bQt?[A-Z][a-z]\w+\b"), "pyqt"))
        self._rules.append((QRegExp(r"\b@\w+\b"), "decorator"))
        string_re = QRegExp(r"""(?:'[^']*'|"[^"]*")""")
        string_re.setMinimal(True)
        self._rules.append((string_re, "string"))
        self.stringRe = QRegExp(r"""(:?"["]".*"["]"|'''.*''')""")
        self.stringRe.setMinimal(True)
        self._rules.append((self.stringRe, "string"))

    def _initialize_formats(self):
        baseFormat = QTextCharFormat()
        baseFormat.setFontFamily("Consolas")
        baseFormat.setFontPointSize(10)
        for name, color in (("normal", Qt.black),
                            ("keyword", Qt.darkBlue), 
                            ("builtin", Qt.darkRed),
                            ("constant", Qt.darkGreen),
                            ("decorator", Qt.darkBlue), 
                            ("comment", Qt.darkGreen),
                            ("string", Qt.darkYellow), 
                            ("number", Qt.darkMagenta),
                            ("Error", Qt.darkRed), 
                            ("fail", Qt.darkRed),
                            ("FAIL", Qt.darkRed), 
                            ("error", Qt.darkRed),
                            ('end', Qt.white)):
            _format = QTextCharFormat(baseFormat)
            _format.setForeground(QColor(color))
            if name in ("keyword", "decorator"):
                _format.setFontWeight(QFont.Bold)
            if name == "comment":
                _format.setFontItalic(True)
            self._formats[name] = _format

    def highlightBlock(self, text):
        if not text:
            return
        for regex, _format in self._rules:
            i = regex.indexIn(text)
            while i >= 0:
                length = regex.matchedLength()
                if self._formats.get(_format):
                    self.setFormat(i, length,
                                self._formats[_format])
                i = regex.indexIn(text, i + length)
