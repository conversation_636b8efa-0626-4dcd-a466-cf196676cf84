# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''

from PyQt5.Qt import Qt, <PERSON><PERSON><PERSON><PERSON>, QFont, QCursor
from PyQt5.QtWidgets import QPushButton, QHBoxLayout, QLineEdit

from controller.system_plugin.edit.view.component.Documentation import Documentation
from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.component.table.Table import Table
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader


class DefaultEdit(QWidget):

    def __init__(self, parent):
        super().__init__()
        self._parent_window = parent
        self._is_visible = True

    def load(self):
        self._path = self._set_name_area()
        self._settings = self._set_settings_button()
        self._documentation = self._set_documentation_area()
        self._teardown = self._set_teardown_area()
        self._timeout = self._set_timeout_area()
        self._table = self._set_table_area()

    def _set_name_area(self):
        self._name_line = QLineEdit()
        self._name_line.setFont(QFont("Roman times", 10, QFont.Bold))
        self._name_line.setStyleSheet("border-width:0;border-style:outset")
        self._name_line.setReadOnly(True)
        layout = QHBoxLayout()
        layout.addWidget(self._name_line)
        return layout

    def _set_settings_button(self):
        layout = QHBoxLayout()
        self._settings_btn = QPushButton(LanguageLoader().get('SETTINGS'), self._parent_window)
        self._settings_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._settings_btn.setFixedSize(120, 25)
        self._settings_btn.clicked.connect(self._set_visible)
        layout.addWidget(self._settings_btn, 0, Qt.AlignLeft)
        return layout

    def _set_documentation_area(self):
        documentation = Documentation(self._parent_window)
        documentation.load()
        return documentation

    def _set_teardown_area(self):
        teardown = LineEditArea(self._parent_window, 'Teardown')
        teardown.load()
        return teardown

    def _set_timeout_area(self):
        timeout = LineEditArea(self._parent_window, 'Timeout')
        timeout.load()
        return timeout

    def _set_table_area(self):
        table = Table(self._parent_window)
        table.load()
        return table

    def _set_visible(self):
        if self._is_visible:
            self._is_visible = False
            self._set_visible_area()
        else:
            self._is_visible = True
            self._set_visible_area()

    def _set_visible_area(self):
        self._documentation.set_visible(self._is_visible)
        self._teardown.set_visible(self._is_visible)
        self._timeout.set_visible(self._is_visible)

    def fill_data(self, parsed_item):
        self._name_line.setText(CurrentItem().get().get('name'))
        self._name_line.setCursorPosition(0)
        self._documentation.fill_data(parsed_item.documentation)
        self._teardown.fill_data(parsed_item.teardown)
        self._timeout.fill_data(parsed_item.timeout)
        self._table.fill_data(parsed_item)
