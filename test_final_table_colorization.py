#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终测试表格文字染色修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor

def create_robot_test_table():
    """创建RobotFramework测试表格"""
    table = QTableWidget(12, 6)
    table.setHorizontalHeaderLabels(['关键字', '参数1', '参数2', '参数3', '参数4', '注释'])
    
    # RobotFramework测试数据
    test_data = [
        ['*** Test Cases ***', '', '', '', '', ''],
        ['测试用例1', '', '', '', '', ''],
        ['    Log', 'Hello World', '', '', '', '# 输出日志'],
        ['    ${variable}', '=', 'Set Variable', 'test_value', '', ''],
        ['    FOR', '${item}', 'IN', '@{list}', '', ''],
        ['        Log', '${item}', '', '', '', ''],
        ['    END', '', '', '', '', ''],
        ['    Sleep', '1s', '', '', '', '# 等待1秒'],
        ['    Should Be Equal', '${variable}', 'test_value', '', '', ''],
        ['    ${result}', '=', 'Get Text', 'xpath=//div', '', ''],
        ['    Comment', '这是一个注释行', '', '', '', ''],
        ['    [Arguments]', '${arg1}', '${arg2}', '', '', '']
    ]
    
    for row, row_data in enumerate(test_data):
        for col, cell_data in enumerate(row_data):
            item = QTableWidgetItem(cell_data)
            table.setItem(row, col, item)
    
    return table

def apply_colorizer_to_table(table):
    """使用Colorizer对表格进行染色"""
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
        
        # 创建模拟的Table对象
        class MockTable:
            def __init__(self, qt_table):
                self._table = qt_table
        
        mock_table = MockTable(table)
        colorizer = Colorizer()
        
        print("开始使用Colorizer染色表格...")
        
        for row in range(table.rowCount()):
            line = []
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    line.append(item.text())
                else:
                    line.append('')
            
            if any(line):  # 只染色有内容的行
                colorizer.colorize(mock_table, row, line)
        
        print("Colorizer染色完成")
        
    except Exception as e:
        print(f"Colorizer染色失败: {e}")
        import traceback
        traceback.print_exc()

def test_theme_switching():
    """测试主题切换"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        print(f"当前主题: {current_theme}")
        
        # 切换到深色橙色主题
        if current_theme != 'dark_orange':
            print("切换到深色橙色主题...")
            result = theme_manager.set_theme('dark_orange')
            print(f"切换结果: {result}")
            
            new_theme = theme_manager.get_current_theme()
            print(f"切换后主题: {new_theme}")
            
            return 'dark_orange'
        else:
            print("已经是深色橙色主题")
            return current_theme
            
    except Exception as e:
        print(f"主题切换失败: {e}")
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("深色橙色主题表格文字染色修复验证")
    main_window.resize(1200, 800)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
深色橙色主题表格文字染色修复验证

修复内容:
1. 移除CSS中表格单元格的固定颜色设置
2. 修复ColorizationSettings的主题颜色获取
3. 修复Colorizer的颜色缓存机制
4. 改进关键字识别逻辑

预期效果:
- 关键字(Log, Sleep, FOR, END等)显示为浅蓝色
- 变量(${variable}等)显示为浅绿色
- 注释(#开头)显示为灰色
- 普通文字显示为浅灰色

测试方法:
1. 点击"切换到深色橙色主题"
2. 点击"应用Colorizer染色"
3. 观察表格中文字颜色是否正确
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    switch_theme_btn = QPushButton("切换到深色橙色主题")
    apply_colorizer_btn = QPushButton("应用Colorizer染色")
    test_all_btn = QPushButton("运行完整测试")
    
    button_layout.addWidget(switch_theme_btn)
    button_layout.addWidget(apply_colorizer_btn)
    button_layout.addWidget(test_all_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_robot_test_table()
    layout.addWidget(QLabel("RobotFramework测试表格:"))
    layout.addWidget(table)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    output_text.setMaximumHeight(150)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 连接按钮事件
    def switch_theme():
        output_text.clear()
        theme = test_theme_switching()
        if theme:
            output_text.append(f"主题切换完成: {theme}")
            # 自动应用染色
            apply_colorizer_to_table(table)
            output_text.append("已自动应用Colorizer染色")
        else:
            output_text.append("主题切换失败")
    
    def apply_colorizer():
        output_text.clear()
        apply_colorizer_to_table(table)
        output_text.append("Colorizer染色已应用")
    
    def test_all():
        output_text.clear()
        output_text.append("=== 开始完整测试 ===")
        
        # 1. 切换主题
        theme = test_theme_switching()
        if theme:
            output_text.append(f"✅ 主题切换成功: {theme}")
        else:
            output_text.append("❌ 主题切换失败")
            return
        
        # 2. 应用染色
        try:
            apply_colorizer_to_table(table)
            output_text.append("✅ Colorizer染色应用成功")
        except Exception as e:
            output_text.append(f"❌ Colorizer染色失败: {e}")
            return
        
        # 3. 验证颜色
        output_text.append("✅ 完整测试完成")
        output_text.append("")
        output_text.append("请检查表格中的文字颜色:")
        output_text.append("- 关键字(Log, Sleep, FOR, END)应为浅蓝色")
        output_text.append("- 变量(${variable})应为浅绿色")
        output_text.append("- 注释(# 开头)应为灰色")
        output_text.append("- 普通文字应为浅灰色")
    
    switch_theme_btn.clicked.connect(switch_theme)
    apply_colorizer_btn.clicked.connect(apply_colorizer)
    test_all_btn.clicked.connect(test_all)
    
    # 自动运行测试
    def auto_test():
        output_text.append("=== 自动测试开始 ===")
        test_all()
    
    QTimer.singleShot(1000, auto_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 深色橙色主题表格文字染色修复验证...")
    print("=" * 60)
    
    # 运行控制台测试
    test_theme_switching()
    
    print("\n" + "=" * 60)
    print("启动GUI验证窗口...")
    
    sys.exit(main())
