#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试编码修复 - 验证CSS文件读取不再出现编码错误
"""

import sys
import os
import codecs

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_css_file_encoding():
    """测试CSS文件编码读取"""
    css_files = [
        "resources/qss/default.qss",
        "resources/qss/dark_theme.qss", 
        "resources/qss/dark_orange.qss"
    ]
    
    results = []
    
    for css_file in css_files:
        if not os.path.exists(css_file):
            print(f"❌ CSS文件不存在: {css_file}")
            results.append(False)
            continue
            
        try:
            # 测试使用UTF-8编码读取
            with codecs.open(css_file, "r", encoding='utf-8') as f:
                content = f.read()
            print(f"✅ 成功读取 {css_file} (UTF-8编码，{len(content)}字符)")
            results.append(True)
            
        except UnicodeDecodeError as e:
            print(f"❌ UTF-8编码读取失败 {css_file}: {e}")
            results.append(False)
            
        except Exception as e:
            print(f"❌ 读取文件失败 {css_file}: {e}")
            results.append(False)
    
    return all(results)

def test_mainframe_style_loading():
    """测试MainFrame样式加载"""
    try:
        from settings.Loader import Loader
        
        # 获取默认样式文件路径
        style_file = Loader().get_path('DEFAULT_QSS')
        print(f"默认样式文件路径: {style_file}")
        
        if not os.path.exists(style_file):
            print(f"❌ 默认样式文件不存在: {style_file}")
            return False
        
        # 测试使用UTF-8编码读取（修复后的方式）
        with codecs.open(style_file, "r", encoding='utf-8') as f:
            style = f.read()
        
        print(f"✅ 成功读取默认样式文件 (UTF-8编码，{len(style)}字符)")
        
        # 检查样式内容是否包含中文注释
        if "深色主题" in style or "浅色主题" in style or "表格" in style:
            print("✅ 样式文件包含中文注释，UTF-8编码正确")
        
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ MainFrame样式文件UTF-8编码读取失败: {e}")
        return False
        
    except Exception as e:
        print(f"❌ MainFrame样式文件读取失败: {e}")
        return False

def test_theme_manager_loading():
    """测试ThemeManager样式加载"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager, THEMES
        
        theme_manager = ThemeManager()
        
        for theme_id, theme_info in THEMES.items():
            style_file = os.path.join("resources", "qss", theme_info['file'])
            
            if not os.path.exists(style_file):
                print(f"❌ 主题文件不存在: {style_file}")
                continue
            
            try:
                # 测试ThemeManager的文件读取方式
                with codecs.open(style_file, "r", encoding='utf-8') as f:
                    style = f.read()
                
                print(f"✅ ThemeManager成功读取 {theme_id} 主题文件 (UTF-8编码，{len(style)}字符)")
                
            except UnicodeDecodeError as e:
                print(f"❌ ThemeManager读取 {theme_id} 主题文件编码失败: {e}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ ThemeManager测试失败: {e}")
        return False

def main():
    print("🔍 测试编码修复...")
    print("=" * 60)
    
    results = []
    
    print("\n1. 测试CSS文件编码读取:")
    results.append(test_css_file_encoding())
    
    print("\n2. 测试MainFrame样式加载:")
    results.append(test_mainframe_style_loading())
    
    print("\n3. 测试ThemeManager样式加载:")
    results.append(test_theme_manager_loading())
    
    print("\n" + "=" * 60)
    
    if all(results):
        print("🎉 所有编码测试通过！")
        print("\n修复总结:")
        print("✅ MainFrame.py中的_set_global_style()方法已修复编码问题")
        print("✅ 所有CSS文件都可以正确使用UTF-8编码读取")
        print("✅ ThemeManager可以正确读取所有主题文件")
        print("✅ 不再出现UnicodeDecodeError错误")
        print("\n现在应用启动时不会再出现编码错误！")
        return True
    else:
        print("❌ 部分编码测试失败，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
