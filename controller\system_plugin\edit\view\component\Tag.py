# encoding=utf-8
'''
Created on 2019年11月6日

@author: 10247557
'''
from PyQt5.Qt import Qt, QBrush, QColor
from PyQt5.QtWidgets import <PERSON><PERSON>abel, QTableWidget, QHBoxLayout, QTableWidgetItem

from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from utility.ShowStatusSwitcher import switch_show_status


class Tag():

    def __init__(self, parent, tag_name):
        self._parent = parent
        self._tag_name = tag_name
        self._content = None
        self._parent_column = 0

    def get_layout(self):
        return self._layout

    def load(self):
        self._label = QLabel(self._tag_name)
        self._label.setFixedWidth(170)
#         self._label.resize(30, 30)
        self._set_table()
        self._empty_label = QLabel('                   ')
        self._set_layout()

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._label, 1)
        self._layout.addWidget(self._table, 6)
        self._layout.addWidget(self._empty_label)

    def _set_table(self):
        self._table = QTableWidget(self._parent)
        self._table.setColumnCount(1)
        self._table.setRowCount(1)
        self._table.horizontalHeader().setVisible(False)
        self._table.verticalHeader().setVisible(False)
        self._set_table_style()
        self._table.setFixedHeight(50)
        self._table.itemChanged.connect(self._auto_extension)
        self._table.itemChanged.connect(self._modify_data)
        self._table.itemChanged.connect(self._delete_empty_item)
#         self._table.setEditTriggers(QAbstractItemView.DoubleClicked)

        # 连接主题变化信号
        self._connect_theme_signal()

    def _set_table_style(self):
        """设置表格样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if theme_manager.is_dark_theme():
                style = """
                    QTableWidget {
                        border: none;
                        background-color: #404040;
                        color: #E0E0E0;
                        gridline-color: #555555;
                    }
                    QTableWidget::item {
                        background-color: #404040;
                        color: #E0E0E0;
                        border: 1px solid #555555;
                        padding: 2px;
                    }
                    QTableWidget::item:selected {
                        background-color: #4A90E2;
                        color: #FFFFFF;
                    }
                """
            else:
                style = "border:none;"

            self._table.setStyleSheet(style)
        except Exception as e:
            print(f"设置Tags表格样式失败: {e}")
            self._table.setStyleSheet("border:none;")

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接Tags主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._set_table_style()

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._table.setVisible(bool_value)
        self._empty_label.setVisible(bool_value)

    def get_data(self):
        all_column = self._table.columnCount()
        line, has_value = [], False
        for column in range(all_column, self._parent_column - 1, -1):
            item = self._table.item(0, column)
            if item and item.text().strip():
                has_value = True
                line.append(item.text())
            elif has_value:
                line.append('')
        line.reverse()
        if line and line[0] == '':
            line.pop(0)
        result = ' | '.join(line)
        return result

    def reset_table(self):
        self._table.clear()
        self._table.setColumnCount(1)
        self._table.setRowCount(1)

    def fill_data(self, content):
        self._parent_column = 0

        @switch_show_status(self)
        def _fill_data(self, content):
            self.reset_table()
            parent_tag = LocalRepoUpdater.PARENT_TAGS
            if parent_tag:
                self._fill_parent_data(parent_tag)
            if content:
                self._content = ' | '.join(content)
                line_list = content[0].split(' | ')
                for index in range(len(line_list)):
                    item = QTableWidgetItem(line_list[index])
                    item.setTextAlignment(Qt.AlignCenter)
                    self._table.setItem(0, index + self._parent_column, item)
                    all_column = self._table.columnCount()
                    if index + self._parent_column + 1 == all_column:
                        self._table.insertColumn(all_column)
            else:
                self._content = None
        return _fill_data(self, content)

    def _fill_parent_data(self, parent_tag):
        for index in range(len(parent_tag)):
            item = QTableWidgetItem(parent_tag[index])
            item.setTextAlignment(Qt.AlignCenter)
            item.setForeground(QBrush(QColor(255, 0, 0)))
            item.setBackground(QBrush(QColor(225, 225, 225)))
            item.setFlags(item.flags() & (Qt.ItemIsEditable))
            self._table.setItem(0, index, item)
            all_column = self._table.columnCount()
            if index + 1 == all_column:
                self._table.insertColumn(all_column)
        self._parent_column = self._table.columnCount() - 1

    def _auto_extension(self):
        select_value = self._table.selectedItems()
        if select_value:
            value = select_value[0].text()
            column = self._table.selectedIndexes()[0].column()
            all_column = self._table.columnCount()
            if value.strip():
                if column + 1 == all_column:
                    self._table.insertColumn(all_column)

    def _delete_empty_item(self):
        all_column = self._table.columnCount()
        for column in range(all_column - 1):
            if self._table.item(0, column) and not self._table.item(0, column).text().strip():
                self._table.removeColumn(column)

    def _modify_data(self):
        result = self.get_data()
        if not self._is_show and self._content != result:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify(self._tag_name.replace(' ', '_').lower(), result)
