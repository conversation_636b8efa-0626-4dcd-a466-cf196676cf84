#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的颜色设置测试
验证主题管理器和颜色设置逻辑
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_theme_manager():
    """测试主题管理器"""
    print("=== 测试主题管理器 ===")
    
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器实例
        theme_manager = ThemeManager()
        
        # 测试不同主题
        themes = ['light', 'dark', 'dark_orange']
        
        for theme in themes:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 模拟设置主题（不保存偏好设置）
            theme_manager._current_theme = theme
            current_theme = theme_manager.get_current_theme()
            is_dark = theme_manager.is_dark_theme(theme)
            
            print(f"当前主题: {current_theme}")
            print(f"是否为深色主题: {is_dark}")
            
            # 测试颜色设置逻辑
            if current_theme == 'dark':
                bg_with_content = "#353535"
                bg_without_content = "#2A2A2A"
                text_color = "#E0E0E0"
            elif current_theme == 'dark_orange':
                bg_with_content = "#3A3A3A"
                bg_without_content = "#2D2D2D"
                text_color = "#b1b1b1"
            else:
                bg_with_content = "#ffffff"
                bg_without_content = "#c0c0c0"
                text_color = "#000000"
            
            print(f"有内容背景色: {bg_with_content}")
            print(f"无内容背景色: {bg_without_content}")
            print(f"文字颜色: {text_color}")
            
            # 验证颜色值是否合理
            def is_valid_color(color):
                return color.startswith('#') and len(color) == 7
            
            bg_with_valid = is_valid_color(bg_with_content)
            bg_without_valid = is_valid_color(bg_without_content)
            text_valid = is_valid_color(text_color)
            
            print(f"有内容背景色格式正确: {'✅' if bg_with_valid else '❌'}")
            print(f"无内容背景色格式正确: {'✅' if bg_without_valid else '❌'}")
            print(f"文字颜色格式正确: {'✅' if text_valid else '❌'}")
            
    except Exception as e:
        print(f"测试主题管理器时出错: {e}")
        import traceback
        traceback.print_exc()

def test_color_logic():
    """测试颜色设置逻辑"""
    print("\n=== 测试颜色设置逻辑 ===")
    
    # 模拟LineEditArea的_set_color方法逻辑
    def simulate_set_color(current_theme, has_content):
        if has_content:
            # 有内容时的背景色
            if current_theme == 'dark':
                return "background-color: #353535; color: #E0E0E0;"
            elif current_theme == 'dark_orange':
                return "background-color: #3A3A3A; color: #b1b1b1;"
            else:
                return "background-color: #ffffff; color: #000000;"
        else:
            # 无内容时的背景色
            if current_theme == 'dark':
                return "background-color: #2A2A2A; color: #A0A0A0;"
            elif current_theme == 'dark_orange':
                return "background-color: #2D2D2D; color: #808080;"
            else:
                return "background-color: #c0c0c0; color: #000000;"
    
    themes = ['light', 'dark', 'dark_orange']
    
    for theme in themes:
        print(f"\n--- 主题: {theme} ---")
        
        style_with_content = simulate_set_color(theme, True)
        style_without_content = simulate_set_color(theme, False)
        
        print(f"有内容样式: {style_with_content}")
        print(f"无内容样式: {style_without_content}")
        
        # 验证样式字符串格式
        def validate_style(style):
            return 'background-color:' in style and 'color:' in style
        
        with_content_valid = validate_style(style_with_content)
        without_content_valid = validate_style(style_without_content)
        
        print(f"有内容样式格式正确: {'✅' if with_content_valid else '❌'}")
        print(f"无内容样式格式正确: {'✅' if without_content_valid else '❌'}")

def test_table_edit_area_logic():
    """测试表格编辑区域颜色逻辑"""
    print("\n=== 测试表格编辑区域颜色逻辑 ===")
    
    def simulate_table_edit_area_style(current_theme):
        if current_theme == 'dark':
            return """
                QTextEdit {
                    border: none;
                    background-color: #353535;
                    color: #E0E0E0;
                    selection-background-color: #4A90E2;
                    selection-color: #FFFFFF;
                }
            """
        elif current_theme == 'dark_orange':
            return """
                QTextEdit {
                    border: none;
                    background-color: #3A3A3A;
                    color: #b1b1b1;
                    selection-background-color: QLinearGradient( x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ffa02f, stop: 1 #d7801a);
                    selection-color: #000000;
                }
            """
        else:
            return "border: none;background-color: #ececec;color: #000000;"
    
    themes = ['light', 'dark', 'dark_orange']
    
    for theme in themes:
        print(f"\n--- 主题: {theme} ---")
        
        style = simulate_table_edit_area_style(theme)
        print(f"表格编辑区域样式: {style.strip()}")
        
        # 验证样式包含必要的属性
        has_background = 'background-color:' in style
        has_color = 'color:' in style
        has_border = 'border:' in style
        
        print(f"包含背景色: {'✅' if has_background else '❌'}")
        print(f"包含文字颜色: {'✅' if has_color else '❌'}")
        print(f"包含边框设置: {'✅' if has_border else '❌'}")

def main():
    """主测试函数"""
    print("开始测试编辑页签背景色设置逻辑...")
    
    try:
        test_theme_manager()
        test_color_logic()
        test_table_edit_area_logic()
        
        print("\n=== 测试完成 ===")
        print("所有逻辑测试已完成，请检查输出结果")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
