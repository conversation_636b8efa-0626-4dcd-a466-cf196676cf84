# coding=utf-8
'''
Created on 2019年11月8日

@author: 10247557
'''
import gc
import os
import webbrowser

from PyQt5.Qt import QIcon, QThread, pyqtSignal

from controller.test_runner.LogEditParser import WorkThread
from model.data_file.DataFile import SaveSequence
from resources.Loader import Loader
from robot.rebot import rebot
from settings.LogProject import LogProject
from settings.i18n.Loader import LanguageLoader
from utility.LogPathRepository import LogPathRepository
from utility.PluginRepository import PluginRepository
from utility.UIRepository import UIRepository
from utility.timer import get_date_time
from view.common.MessageBox import MessageBox


class BtnAction(object):

    def __init__(self):
        self._stop_pressed_count = 0

    def run(self, cmds, btn_list):
        self._stop_pressed_count = 0
        BtnAction._clear_log()
        self._wt = WorkThread()
        self._wt.set_cmds(cmds)
        self._wt.set_btn(btn_list)
        self._append_running_log('Testcases is running...\n')
        self._wt.new_summary_log.connect(BtnAction._append_summary_log)
        self._wt.new_trace_log.connect(BtnAction._append_trace_log)
        self._wt.new_progress_log.connect(BtnAction._update_progress_messge)
        self._wt.icon_status.connect(BtnAction._set_testcase_icon)
        self._wt.start()
        self.log_thread_stoped = True

    def pause(self, parent):
        self._set_btn_status_in_pause(parent)
        self._wt.new_summary_log.emit('[ SENDING PAUSE SIGNAL ]\n')
        self._wt._plugin.on_pause()

    def continue_(self, parent):
        parent._pause_btn.setEnabled(True)
        parent._stop_btn.setEnabled(True)
        parent._continue_btn.setEnabled(False)
        self._wt.new_summary_log.emit('[ SENDING CONTINUE SIGNAL ]\n')
        self._wt._plugin.on_continue()

    def stop(self, parent):
        self._wt.new_summary_log.emit('[ SENDING STOP SIGNAL ]\n')
        self._wt._plugin.on_stop()
        self._stop_pressed_count += 1
        if self._stop_pressed_count >= 2:
            if not PluginRepository().find('SHOW_REAL_TIME_LOG'):
                log_btn_gray = True
                self._wt.set_log_btn_status(log_btn_gray)
                log_btn_gray = False
            self._set_btn_status_in_two_stop(parent)
            self._write_file(need_open_log=False)

    def next(self):
        self._wt.new_summary_log.emit('[ SENDING STEP NEXT SIGNAL ]\n')
        self._wt._plugin.on_stop()

    def over(self):
        self._wt.new_summary_log.emit('[ SENDING STEP OVER SIGNAL ]\n')
        self._wt._plugin.on_stop()

    def get_log(self):
        if self._wt.isRunning():
            self._write_file(need_open_log=True)
        else:
            if os.path.exists(LogPathRepository().find('output_dir') + os.path.sep+'log.html'):
                webbrowser.open(LogPathRepository().find('output_dir') + os.path.sep+'log.html')
            else:
                MessageBox().show_information(LanguageLoader().get('LOG_FILE_SHOW_TIPS'))

    def _write_file(self, need_open_log=True):
        if PluginRepository().find('SHOW_REAL_TIME_LOG') and self.log_thread_stoped:
            self.log_thread_stoped = False
            self._realtime_log_thread = RealtimeLogThread(self, need_open_log)
            self._realtime_log_thread.start()
            log_label = PluginRepository().find('KEYWORD_PROGRESS')
            log_label.setText('实时日志生成中，请稍等...')
            self._realtime_log_thread.log_finished.connect(self._set_log_label)
            status_bar = PluginRepository().find('STATUS_BAR')
            status_bar.addPermanentWidget(log_label)

    def _set_log_label(self, text):
        log_label = PluginRepository().find('KEYWORD_PROGRESS')
        log_label.setText(text)

    @staticmethod
    def _clear_log():
        log_trace_editor = UIRepository().find('log_trace_editor')
        summary_log_editor = UIRepository().find('log_summary_editor')
        progress_editor = UIRepository().find('progress_editor')
        progress_editor.clear()
        summary_log_editor.clear()
        log_trace_editor.clear()

        # 根据主题设置progress_editor样式
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if theme_manager.is_dark_theme():
                progress_editor.setStyleSheet("background-color: #404040; color: #E0E0E0; border-width:0; border-style:outset")
            else:
                progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")
        except Exception as e:
            print(f"设置progress_editor主题样式失败: {e}")
            progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")

    @staticmethod
    def _update_progress_messge(message, plugin):
        progress_editor = UIRepository().find('progress_editor')
        progress_editor.setText(message)
        pass_num = plugin.progress_bar._pass
        fail_num = plugin.progress_bar._fail

        # 根据主题和执行状态设置样式
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if pass_num > 0 and fail_num == 0:
                # 成功状态 - 绿色背景
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #2E7D32; color: #E8F5E8; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(155,213,139)")
            elif fail_num > 0:
                # 失败状态 - 红色背景
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #C62828; color: #FFEBEE; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(242,125,124)")
            else:
                # 默认状态
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #404040; color: #E0E0E0; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")
        except Exception as e:
            print(f"设置progress_editor执行状态样式失败: {e}")
            # 回退到原始样式
            if pass_num > 0 and fail_num == 0:
                progress_editor.setStyleSheet("background:rgb(155,213,139)")
            elif fail_num > 0:
                progress_editor.setStyleSheet("background:rgb(242,125,124)")

    @staticmethod
    def _append_trace_log(log):
        log_trace_editor = UIRepository().find('log_trace_editor')
        log_trace_editor.append(log[:1000000])

    def _append_running_log(self, log):
        summary_log_editor = UIRepository().find('log_summary_editor')
        summary_log_editor.append(log)

    @staticmethod
    def _append_summary_log(log):
        summary_log_editor = UIRepository().find('log_summary_editor')
        summary_log_editor.append(log)

    @staticmethod
    def _set_testcase_icon(testcase, status):
        if status == 'Running':
            testcase.setIcon(0, QIcon(Loader().get_path('RUNNING')))
        elif status == 'Failed':
            testcase.setIcon(0, QIcon(Loader().get_path('FAILED')))
        elif status == 'Passed':
            testcase.setIcon(0, QIcon(Loader().get_path('PASSED')))

    def _set_btn_status_in_two_stop(self, parent):
        parent._start_btn.setEnabled(True)
        parent._pause_btn.setEnabled(False)
        parent._stop_btn.setEnabled(False)
        parent._continue_btn.setEnabled(False)
        parent._log_btn.setEnabled(True)
        PluginRepository().update('TESTCASE_RUNNING', False)
        SaveSequence.save_all()

    def _set_btn_status_in_pause(self, parent):
        parent._start_btn.setEnabled(False)
        parent._pause_btn.setEnabled(False)
        parent._stop_btn.setEnabled(True)
        parent._continue_btn.setEnabled(True)
        parent._log_btn.setEnabled(True)


class RealtimeLogThread(QThread):

    log_finished = pyqtSignal(str)

    def __init__(self, action, need_open_log):
        super().__init__()
        self._action = action
        self._need_open_log = need_open_log

    def run(self):
        path = LogPathRepository().find('output_dir')
        self._action._wt.write_xml()
        rebot(path + os.path.sep+ 'out.xml', outputdir=path, logtitle='实时日志%s' % get_date_time()[:-4])
        if os.path.exists(path + os.path.sep+'log.html'):
            self.log_finished.emit('实时日志生成完成。。。')
            if self._need_open_log:
                webbrowser.open(path + os.path.sep+ 'log.html')
        else:
            self.log_finished.emit('实时日志生成异常。。。')
        self._action.log_thread_stoped = True
        try:
            os.remove(path + os.path.sep+'out.xml')
        except:
            pass
        gc.collect()
