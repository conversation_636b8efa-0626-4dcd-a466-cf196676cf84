# coding=utf-8
'''
Created on 2020年3月4日

@author: 10247557
'''
import threading
import time

import requests

from controller.userinfo.UserInfo import UserInfo
from utility.jobschedule.JobScheduler import JobScheduler
from utility.log import SystemLogger


class UserInfoManager():

    def __init__(self):
        self._userInfo = UserInfo()

    def start(self):
        thread = threading.Thread(target=self.send_user_info)
        thread.setDaemon(True)
        thread.start()

    def send_user_info(self):
        self.post()
        s = JobScheduler()
        s.add_cron_job("send_user_info", self.post, args=[], hour='10', minute='0', second='0')
        s.start()
        while True:
            time.sleep(10)

    def post(self):
        url = 'http://zxmte.zte.com.cn/userinfo'
        user_info = self._get_user_info()
        try:
            requests.post(url, user_info)
        except Exception as e:
            SystemLogger.error('connect to server error')

    def _get_user_info(self):
        data = {}
        try:
            date = time.strftime("%Y-%m-%d", time.localtime())
            name = self._userInfo.get_pc_name()
            ip = self._userInfo.get_pc_ip()
            mac_address = self._userInfo.get_mac_address()
            sys_type = self._userInfo.get_operator_system_type()
            data.update({'date': date, 'pcName': name, 'ip': ip,
                         'macAddress': mac_address, 'systemType': sys_type})
        except Exception as e:
            SystemLogger.error(e)
        return data

if __name__ == "__main__":
    import time
    userInfoManager = UserInfoManager()
    userInfoManager.start()
    time.sleep(10)
