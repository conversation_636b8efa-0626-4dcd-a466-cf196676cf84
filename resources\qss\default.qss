
QPushButton
{
    border-width: 1px;
    border-color: #1e1e1e;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
}

QPushButton:hover
{
	color:rgb(11 , 137 , 234);
   	background-color: QLinearGradient( x1: 0, y1: 0, x2: 0, y2: 1, stop: 0, stop: 0.1, stop: 0.5, stop: 0.9, stop: 1);
    border: 1px solid rgb(11 , 137 , 234);
}

QPushButton:pressed
{
    background-color: QLinearGradient( x1: 0, y1: 0, x2: 0, y2: 1, stop: 0, stop: 0.1, stop: 0.5, stop: 0.9, stop: 1);
    border: 1px solid rgb(12 , 138 , 235);
    padding-left:3px;
    padding-top:3px;
}


/*run button style*/
QPushButton[name='primary']
{
    border-width: 1px;
    border-color: #409EFF;
    color:#409EFF;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
}

QPushButton[name='succ']
{
    border-width: 1px;
    border-color: #67C23A;
    color:#67C23A;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
}

QPushButton[name='warn']
{
    border-width: 1px;
    border-color: #E6A23C;
    color:#E6A23C;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
}

QPushButton[name='danger']
{
    border-width: 1px;
    border-color: #F56C6C;
    color:#F56C6C;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
}

QPushButton:pressed[name='succ']
{
    background-color: #67C23A;
    border: 1px solid rgb(12 , 138 , 235);
    color:black;
    padding-left:3px;
    padding-top:3px;
}

QPushButton:pressed[name='warn']
{
    background-color: #E6A23C;
    border: 1px solid rgb(12 , 138 , 235);
    color:black;
    padding-left:3px;
    padding-top:3px;
}

QPushButton:pressed[name='danger']
{
    background-color: #F56C6C;
    color:black;
    border: 1px solid rgb(12 , 138 , 235);
    padding-left:3px;
    padding-top:3px;
}

QPushButton:pressed[name='primary']
{
    background-color: #409EFF;
    color:black;
    border: 1px solid rgb(12 , 138 , 235);
    padding-left:3px;
    padding-top:3px;
}

/* 表格样式 */
QTableWidget, QTableView {
    background-color: #FFFFFF;
    color: #000000;
    gridline-color: #D0D0D0;
    border: 1px solid #D0D0D0;
}

/* 表格表头 */
QHeaderView::section {
    background-color: #F5F5F5;
    color: #000000;
    border: 1px solid #D0D0D0;
    padding: 4px;
}

QHeaderView::section:hover {
    background-color: #E0E0E0;
}

/* 表格左上角corner button - 浅色主题 */
QTableView QTableCornerButton::section, QTableWidget QTableCornerButton::section {
    background-color: #F5F5F5;
    border: 1px solid #D0D0D0;
}

QTableCornerButton::section {
    background-color: #F5F5F5;
    border: 1px solid #D0D0D0;
}

QAbstractScrollArea::corner {
    background-color: #F5F5F5;
    border: 1px solid #D0D0D0;
}

