# -*- coding: utf-8 -*-
import subprocess,platform,difflib,json,re,sys,os,urllib,traceback,datetime,time,socket
from threading import Thread
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiStudioApi
from controller.system_plugin.rf_assistant.RF_Helper.Thread.LoadGlobalConfigThread import LoadGlobalConfigThread
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3
from controller.system_plugin.rf_assistant.RF_Helper.api.RdcApi import Rdc<PERSON><PERSON>
from controller.system_plugin.rf_assistant.RF_Helper.ui.AboutDialog import AboutDialog
from controller.system_plugin.rf_assistant.RF_Helper.ui.FeedbackDialog import Feedback<PERSON>ialog
from controller.system_plugin.rf_assistant.RF_Helper.ui.LoginDialogWindow import Login<PERSON>ialog<PERSON>indow
from controller.system_plugin.rf_assistant.RF_Helper.ui.SetupDialogWindow import <PERSON>upDialogWindow
from controller.system_plugin.rf_assistant.RF_Helper.Thread.parserKeywordsThread import parserKeywordsThread
from controller.system_plugin.rf_assistant.RF_Helper.Thread.AiThread import AiThread
from controller.system_plugin.rf_assistant.RF_Helper.Thread.DatabaseThread import DatabaseThread
from controller.system_plugin.rf_assistant.RF_Helper.ui.DiffTextEdit import DiffTextEdit
from controller.system_plugin.rf_assistant.RF_Helper.ui.TabSSP import TabSSP
from utility.PluginRepository import PluginRepository
from controller.system_plugin.rf_assistant.RF_Helper.Thread.LoadTestcaseKbThread import LoadTestcaseKbThread


TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'


class RFHelperMainFrame(QMainWindow):
    def __init__(self, parent=None):
        super(RFHelperMainFrame, self).__init__()
        self.parent = parent
        self.caseInfoDic = {}
        self.caseInfoDic0 = {}
        self.project = 'Qcell'
        self.devMode = '新手模式'
        self.account = ''
        self.password = ''
        self.token = ''
        self.testcaseList = []
        self.keywordsDir = r'D:\script_v2\5GNR\test\userkeywords'
        self.projectDir = r'D:\script_v2\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell'
        self.basicKeywordProjectMap = {'SSP': 'SSP', 'AAU': 'Qcell'}
        self.testPathSplitMap = {'Qcell': 'testcase', '西安多模': 'testcase', 'SSP': 'autobmcv4'}
        self.WORK_SPACE_INFO = {'SSP': 'SAC', 'NE': 'MEC'}
        self.projectDic = {
            '深圳Qcell': "深圳Qcell",
            '管理面_SZ': "管理面_SZ",
            '管理面_XA': "管理面_XA",
            '管理面_SH': "管理面_SH",
            '西安大容量': "西安大容量",
            '西安多模': "西安多模",
            'RT_SH': "RT_SH",
            'CPA_XA': "CPA_XA",
            'CPA_SH': 'CPA_SH',
            'CPA_SZ': "CPA_SZ",
            'SPA_SZ': "SPA_SZ",
            'SPA_XA': "SPA_XA",
            'SPA_SH': "SPA_SH",
            'UPA_SZ': "UPA_SZ",
            'UPA_XA': "UPA_XA",
            'UPA_SH': "UPA_SH",
            'PHY_SZ': "PHY_SZ",
            'PHY_XA': "PHY_XA",
            'PHY_SH': "PHY_SH",
            'OAM_SZ': "OAM_SZ",
            'OAM_XA': "OAM_XA",
            'IM-TOOL_SZ': "IM-TOOL_SZ",
            'IM-TOOL_XA': "IM-TOOL_XA",
            'IM-TOOL_SH': "IM-TOOL_SH",
            'IM-TOOL_SH': "IM-TOOL_SH",
            'IM-SON_SZ': "IM-SON_SZ",
            'IM-SON_XA': "IM-SON_XA",
            'IM-SON_SH': "IM-SON_SH",
            'IM-IPA': "IM-IPA",
            'CRV_SZ': "CRV_SZ",
            'CRV_XA': "CRV_XA",
            'TDDRRU': "TDDRRU",
            'SSP': "SSP",
            'NE': "NE",
        }
        self.TEST_CASE_PATH_INFO = {
            '深圳Qcell': [r'D:\script_v3\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell'],
            'RT_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\03_RT'],
            '管理面_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA\DTV3测试用例（model）\DTV3测试用例（SA）'],
            '管理面_XA': [r'D:\script_v3\5GNR\test\testcases\XA_ITRAN_MODEL'],
            '管理面_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\CM'],
            '西安大容量': [r'D:\script_v3\5GNR\test\testcases\Performance'],
            '西安多模': [r'D:\script_v3\5GNR\test\testcases\UnifiedTestcase_ITRAN'],
            'CPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA'],
            'CPA_XA': [r'D:\script_v3\5GNR\test\testcases\SA',
                       r'D:\script_v3\5GNR\test\testcases\NSA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team11',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team5_Sub1g',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_CPA_Team2',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team1_Sub1g',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team10',
                       r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team2',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_CPA1_NRDC',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_UPA_Team1',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team10',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_UPA',
                       ],
            'CPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\CPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA'],
            'SPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_AutoTest_DT'],
            'SPA_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_02_SPA'],
            'SPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\SPA'],
            'UPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_03_UPA'],
            'UPA_XA': [r'D:\script_v3\5GNR\test\testcases\NSA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_UPA_Team1',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_UPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_03_UPA',],
            'UPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\UPA'],
            'PHY_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_04_PHY'],
            'PHY_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_XA_PHY'],
            'PHY_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\PHY'],
            'OAM_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_05_产品OAM'],
            'OAM_XA': [r'D:\script_v3\5GNR\test\testcases\XA_NSA_NCM',
                       r'D:\script_v3\5GNR\test\testcases\NR3_05_产品OAM'],
            'IM-TOOL_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\DT'],
            'IM-TOOL_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\XA_DOMAIN'],
            'IM-TOOL_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\IM'],
            'IM-SON_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\03_频谱扫描|26_MDT\1vsw3vbpe5nr5lte',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\LCS',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\半静态频谱共享',
                          r'D:\script_v3\5GNR\test\testcases\NSA\son\dt',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\功率共享',
                          ],
            'IM-SON_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON',
                          r'D:\script_v3\5GNR\test\testcases\SA\dt',
                          r'D:\script_v3\5GNR\test\testcases\NSA\son\dt'],
            'IM-SON_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\Raw_Requirement_8293538_NR高频30'],
            'IM-IPA': [r'D:\script_v3\5GNR\test\testcases\IM-IPA'],
            'CRV_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_02_SPA\CA'],
            'CRV_XA': [r'D:\script_v3\5GNR\test\testcases\SA\cloudradio'],
            'CPE_XA': [r'D:\script_v3\5GNR\test\testcases\NR_XA_ATG'],
            'SSP': [r'D:\workspace\autobmcv4\SYSTEST'],
            'TDDRRU': [r'D:\script_v3\5GNR\test\testcases\NR_XA_RAN3_TDDRRU'],
        }
        self.llmItems = ['电信大模型-Saturn(3.0)','nebulacoder-v6.0', 'nebulacoder-cot-v6.0']
        self.llmNameDic = {'星云研发大模型': 'nebulacoder', '电信大模型-Saturn(3.0)': 'ZTEAIM-Saturn'}

        self.load_saved_para()
        self.basicKeywordDic = {}
        self.testKeywordDic = {}
        self.basicKeywordList = []
        self.testKeywordList = []
        self.basicKeywordContentDic = {}
        self.testKeywordContentDic = {}
        self.basicKeywordTestcaseDic = {}
        self.testKeywordTestcaseDic = {}
        self.db = MongoDb3()
        self.setWindowTitle(f"RF开发助手  (当前项目：{self.project})")
        self.resize(800, 500)
        self.setWindowIcon(QIcon('./images/ai.ico'))
        self.initToolBar()
        screen = QDesktopWidget().screenGeometry()
        self.move(screen.width() - self.width() - 10, 10)
        self.tab1 = QWidget()
        self.tab2 = QWidget()
        self.tab3 = QWidget()
        self.tab5 = QWidget()
        self.tab4 = QWidget()

        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)
        self.tab_widget.addTab(self.tab1,'相似脚本推荐')
        self.tab_widget.addTab(self.tab2,'关键字查找')
        if 'SSP' not in self.project:
            self.tab_widget.addTab(self.tab3,'AI生成脚本')
        else:
            self.tab_widget.addTab(self.tab5,'AI生成脚本(SSP)')
            self.tab_widget.addTab(self.tab3,'AI生成脚本')
#         self.tab_widget.addTab(self.tab4,'脚本分析改进')
        self.initTab1Ui()
        self.initTab2Ui()
        self.initTab3Ui()
        self.initTab5Ui()
        self.init_text_buffer_timer()
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.check_token()
        self.start_load_global_config()
        self.start_load_testcase_kb_list()
        self.parser_keywords_and_load_data({'project': self.project})
        self.start_keep_connect_thread()

    def start_load_global_config(self):
        self.threadConfig = LoadGlobalConfigThread(self)
        self.threadConfig.config_loaded.connect(self.on_config_loaded)
        self.threadConfig.daemon = True
        self.threadConfig.start()

    def on_config_loaded(self, para):
        self.basicKeywordProjectMap = para['config']['basicKeywordProjectMap']
        self.testPathSplitMap = para['config']['testPathSplitMap']
        self.WORK_SPACE_INFO = para['config']['WORK_SPACE_INFO']
        self.projectDic = para['config']['projectDic']
        self.llmItems = para['config']['llmItems']
        self.llmNameDic = para['config']['llmNameDic']
        self.TEST_CASE_PATH_INFO = para['config']['TEST_CASE_PATH_INFO']
        self.llmComboBox.clear()
        self.llmComboBox.addItems(self.llmItems)
        self.llmComboBox.setCurrentIndex(0)

    def resetStatusBar(self, statusBar):
        self.statusBar = statusBar
        self.statusBar.showMessage('Ready', 10000)

    def initTab1Ui(self):
        layout = QVBoxLayout()
        self.queryWidget = QWidget()
        self.queryResultListWidget = QListWidget()
        self.rfEdit1 = DiffTextEdit()
        self.rfEdit1.setWordWrapMode(QTextOption.NoWrap)
        self.queryLabel = QLabel('查询条件：')
        self.queryEdit = QLineEdit('')
        self.queryEdit.setPlaceholderText('用例id或者关键信息')
        self.queryButton = QPushButton("查询")
        self.queryButton.clicked.connect(self.on_button_clicked)
        self.queryEdit.returnPressed.connect(self.on_button_clicked)
        grid = QGridLayout()
        grid.setSpacing(10)
        grid.addWidget(self.queryLabel,1,0,1,1)
        grid.addWidget(self.queryEdit,1,1,1,1)
        grid.addWidget(self.queryButton,1,2,1,1)
        grid.addWidget(self.queryResultListWidget,2,0,1,3)
        self.queryWidget.setLayout(grid)
        self.splitter = QSplitter(Qt.Horizontal, self.tab1)
        self.splitter.addWidget(self.queryWidget)
        self.splitter.addWidget(self.rfEdit1)
        self.splitter.setSizes([self.width() * 2 // 5, self.width() * 3 // 5])
        layout.addWidget(self.splitter)
        self.tab1.setLayout(layout)

        self.queryResultListWidget.itemClicked.connect(self.on_item_clicked)
        self.queryResultListWidget.setStyleSheet("QListWidget {"
              "    color: blue;"
              "}")
        self.queryResultListWidget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.queryResultListWidget.customContextMenuRequested.connect(self.show_context_menu)

    def initTab2Ui(self):
        layout = QVBoxLayout()
        self.queryWidget2 = QWidget()
        self.queryResultListWidget2 = QListWidget()
        self.rfEdit2 = DiffTextEdit()
        self.rfEdit2.setWordWrapMode(QTextOption.NoWrap)
        self.rfEdit2.anchorClicked.connect(self.on_item_anchor_clicked2)
        self.queryLabel2 = QLabel('查询条件：')
        self.queryEdit2 = QLineEdit('')
        self.queryButton2 = QPushButton("查询")
        self.baisKeywordCheckBox = QCheckBox("基本关键字", self.tab2)
        self.testKeywordCheckBox = QCheckBox("测试套关键字", self.tab2)
        self.baisKeywordCheckBox.setChecked(True)
        self.testKeywordCheckBox.setChecked(True)
        self.baisKeywordCheckBox.clicked.connect(self.on_basic_keyword_clicked)
        self.testKeywordCheckBox.clicked.connect(self.on_basic_keyword_clicked)
        self.queryEdit2.textChanged.connect(self.on_basic_keyword_clicked)
        self.similarDegreeLabel = QLabel('相似度门限：')
        self.similarDegreeComboBox = QComboBox(self.tab2)
        items = ['0.1', '0.2', '0.3', '0.4', '0.5', '0.6', '0.7', '0.8', '0.9', '1']
        self.similarDegreeComboBox.addItems(items)
        self.similarDegreeComboBox.setCurrentIndex(3)
        grid = QGridLayout()
        grid.setSpacing(5)
        grid.addWidget(self.queryLabel2,2,0,1,1)
        grid.addWidget(self.queryEdit2,2,1,1,1)
        grid.addWidget(self.queryButton2,2,2,1,1)
        grid.addWidget(self.baisKeywordCheckBox,3,0,1,1)
        grid.addWidget(self.testKeywordCheckBox,3,1,1,1)
        grid.addWidget(self.similarDegreeLabel,4,0,1,1)
        grid.addWidget(self.similarDegreeComboBox,4,1,1,1)
        grid.addWidget(self.queryResultListWidget2,5,0,1,3)
        self.queryWidget2.setLayout(grid)
        self.splitter2 = QSplitter(Qt.Horizontal, self.tab1)
        self.splitter2.addWidget(self.queryWidget2)
        self.splitter2.addWidget(self.rfEdit2)
        self.splitter2.setSizes([self.width() * 2 // 5, self.width() * 3 // 5])
        layout.addWidget(self.splitter2)
        self.tab2.setLayout(layout)
        self.queryButton2.clicked.connect(self.on_button2_clicked)
        self.queryResultListWidget2.itemClicked.connect(self.on_item_clicked2)
        self.queryResultListWidget2.setStyleSheet("QListWidget {"
                              "    color: blue;"
                              "}")
        self.similarDegreeComboBox.currentIndexChanged.connect(self.on_similar_degree_combobox_clicked)
        self.queryResultListWidget2.setContextMenuPolicy(Qt.CustomContextMenu)
        self.queryResultListWidget2.customContextMenuRequested.connect(self.show_context_menu2)

    def initTab3Ui(self):
        layout = QVBoxLayout()
        self.queryWidget3 = QWidget()
        self.testcaseNameLabel3 = QLabel('用例名称：')
        self.testcaseNameEdit3 = QLineEdit('')
        self.testcaseNameEdit3.setPlaceholderText("可自动从RDC拉取")
        self.testcaseStepEdit3 = DiffTextEdit()
        self.rfEdit3 = DiffTextEdit()
        self.rfEdit3.setWordWrapMode(QTextOption.NoWrap)
        self.queryLabel3 = QLabel('用例标识：')
        self.queryEdit3 = QLineEdit('')
        self.queryEdit3.setPlaceholderText("标识或用例标识")
        self.queryButton3 = QPushButton("查找用例信息")
        self.llmLabel = QLabel('LLM：')
        self.llmComboBox = QComboBox(self.tab3)
        self.llmComboBox.setToolTip("新手模式建议使用：电信大模型-Saturn(3.0)，专家模式建议使用：nebulacoder-v6.0和nebulacoder-cot-v6.0（思维链模型）")
        items = self.llmItems
        self.llmNameDic = self.llmNameDic
        self.llmComboBox.addItems(items)
        self.llmComboBox.setCurrentIndex(0)
        self.tempLabel = QLabel('temperature')
        self.sliderTempretrue = QSlider(Qt.Horizontal)
        self.sliderTempretrue.setMinimum(0)
        self.sliderTempretrue.setMaximum(10)
        self.sliderTempretrue.setSingleStep(1)
        self.sliderTempretrue.setValue(2)
        self.tempretrue = 0.2
        self.sliderTempretrue.setTickPosition(QSlider.TicksBelow)
        self.sliderTempretrue.setTickInterval(1)
        self.sliderTempretrue.valueChanged.connect(self.on_slider_value_change)
        self.radioLabel3 = QLabel('开发模式：')
        self.radio1Mode = QRadioButton("新手模式")
        self.radio1Mode.setToolTip("此模式下RF助手会查询一条最相似用例，由大模型重构后输出。此模式下推荐使用：电信大模型-Saturn(3.0)")
        self.radio2Mode = QRadioButton("专家模式")
        self.radio2Mode.setToolTip("此模式下RF助手会提供尽可能多的相似用例给大模型，由大模型深入拆解关键字和用例步骤的对应关系，然后仿写出最佳的脚本。此模式下推荐使用：nebulacoder-v6.0和nebulacoder-cot-v6.0（深入思考模型）")

        radio_widget = QWidget()
        radio_widget.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        radio_container = QHBoxLayout(radio_widget)
        radio_container.setSpacing(5)
        radio_container.setContentsMargins(0, 0, 0, 0)
        radio_container.addWidget(self.radio1Mode)
        radio_container.addWidget(self.radio2Mode)

        self.button_group = QButtonGroup()
        self.button_group.addButton(self.radio1Mode)
        self.button_group.addButton(self.radio2Mode)
        self.radio1Mode.setChecked(True)
        self.button_group.buttonClicked.connect(self.on_radio_button_clicked)
        # self.queryLabel33 = QLabel('指定工作区路径：')
        # self.queryEdit33 = QLineEdit('')
        # self.queryEdit33.setPlaceholderText("用来辅助匹配相似脚本，请在左侧导航栏点击用例文件，右键选择“设为主工作区”，可不填。")
        self.queryLabel31 = QLabel('示例脚本：')
        self.queryEdit31 = QLineEdit('')
        self.queryEdit31.setPlaceholderText("用例标识或标识,多个请用逗号分隔。用来给大模型学习的示例脚本，可不填。")
        self.queryButton31 = QPushButton("查找脚本信息")
        self.startButton3 = QPushButton("开始生成")
        self.startButton3.clicked.connect(self.on_start_button3_clicked)
        self.queryButton31.clicked.connect(self.on_query_button31_clicked)
        grid = QGridLayout()
        grid.setSpacing(5)
        grid.addWidget(self.queryLabel3,0,0,1,1)
        grid.addWidget(self.queryEdit3,0,1,1,1)
        grid.addWidget(self.queryButton3,0,2,1,1)
        grid.addWidget(self.llmLabel,1,0,1,1)
        grid.addWidget(self.llmComboBox,1,1,1,1)

        grid.addWidget(self.radioLabel3,3,0,1,1)
        grid.addWidget(radio_widget,3,1,1,1)  # 将容器添加到网格布局
        grid.addWidget(self.testcaseNameLabel3,4,0,1,1)
        grid.addWidget(self.testcaseNameEdit3,4,1,1,2)

        grid.addWidget(self.queryLabel31,5,0,1,1)
        grid.addWidget(self.queryEdit31,5,1,1,1)
        grid.addWidget(self.queryButton31,5,2,1,1)
        # grid.addWidget(self.queryLabel33,6,0,1,1)
        # grid.addWidget(self.queryEdit33,6,1,1,1)
        grid.addWidget(self.testcaseStepEdit3,7,0,1,3)
        grid.addWidget(self.startButton3,8,0,1,3)

        self.queryWidget3.setLayout(grid)
        self.splitter3 = QSplitter(Qt.Horizontal, self.tab3)
        self.splitter3.addWidget(self.queryWidget3)
        self.splitter3.addWidget(self.rfEdit3)
        self.splitter3.setSizes([self.width() * 2 // 5, self.width() * 3 // 5])
        layout.addWidget(self.splitter3)
        self.tab3.setLayout(layout)
        self.queryButton3.clicked.connect(self.on_query_button3_clicked)
        self.similarDegreeComboBox.currentIndexChanged.connect(self.on_similar_degree_combobox_clicked)

    def initTab5Ui(self):
        self.TabSSP = TabSSP(self, {'width': self.width()})
        layout = QVBoxLayout()
        layout.addWidget(self.TabSSP)
        self.tab5.setLayout(layout)


    def on_radio_button_clicked(self):
        selected_button = self.button_group.checkedButton()
        self.devMode = selected_button.text()
        if self.devMode == '新手模式':
            self.llmComboBox.setCurrentIndex(0)
        else:
            self.llmComboBox.setCurrentIndex(1)

    def on_radio_button_toggled4(self):
        self.queryResultListWidget4.clear()
        self.rfEdit4.setPlainText('')
        if self.typeRadio1.isChecked():
            self.similarThresh4.setEnabled(True)
        else:
            self.similarThresh4.setEnabled(False)


    def on_similar_value_change(self):
        self.statusBar.showMessage('相似度门限：0.' + str(self.similarThresh4.value()), 10000)

    def on_start_button4_clicked(self):
        self.queryResultListWidget4.clear()
        self.rfEdit4.setPlainText('')
        if self.typeRadio1.isChecked():
            self._analysis_similar_case()
        elif self.typeRadio2.isChecked():
            self._analysis_null_case()
        elif self.typeRadio3.isChecked():
            self._analysis_comment_case()

    def _analysis_comment_case(self):
        db = MongoDb3()
        env = self.queryEdit4.text()
        case_info = db.query({'env': env, 'project': self.project}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            self.queryResultListWidget4.addItems(['未找到任何该环境部署的脚本'])
            return
        result = []
        for case in case_info:
            temp = []
            if not self.caseInfoDic.get(case['id']):
                self.caseInfoDic[case['id']] = case
            temp.append(case['id'])
            content = case.get('case_content', [])
            commentLineNum = 0
            for line in content:
                stripedLine = line.strip()
                if stripedLine.startswith("Comment"):
                    commentLineNum = commentLineNum + 1
            temp.append(commentLineNum)
            if commentLineNum > 0:
                temp.append(commentLineNum)
                result.append(temp)
        sorted_data = sorted(result, key=lambda x: x[1], reverse=True)
        listItems = []
        for du in sorted_data:
            listItems.append(du[0] + ' 注释行数：' + str(du[1]))
        if not listItems:
            self.queryResultListWidget4.addItems(['未找到带有注释行的脚本'])
        else:
            self.queryResultListWidget4.addItems(listItems)

    def _analysis_null_case(self):
        db = MongoDb3()
        env = self.queryEdit4.text()
        case_info = db.query({'env': env, 'project': self.project}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            self.queryResultListWidget4.addItems(['未找到任何该环境部署的脚本'])
            return
        result = []
        for case in case_info:
            temp = []
            if not self.caseInfoDic.get(case['id']):
                self.caseInfoDic[case['id']] = case
            temp.append(case['id'])
            content = case.get('case_content', [])
            realLineNum = 0
            commentLineNum = 0
            for line in content:
                stripedLine = line.strip()
                if stripedLine.startswith("Comment") or stripedLine.startswith("log") or stripedLine.startswith("sleep") or stripedLine.startswith("#"):
                    commentLineNum = commentLineNum + 1
                else:
                    realLineNum = realLineNum + 1
            if realLineNum == 0:
                temp.append(commentLineNum)
                result.append(temp)
        sorted_data = sorted(result, key=lambda x: x[1], reverse=True)
        listItems = []
        for du in sorted_data:
            listItems.append(du[0] + ' 脚本行数：' + str(du[1]))
        if not listItems:
            self.queryResultListWidget4.addItems(['未找到空脚本'])
        else:
            self.queryResultListWidget4.addItems(listItems)

    def on_item_clicked4(self, item):
        if self.typeRadio1.isChecked():
            self._display_similar_case(item)
        elif self.typeRadio2.isChecked():
            self._display_null_case(item)
        elif self.typeRadio3.isChecked():
            self._display_comment_case(item)

    def _display_comment_case(self, item):
        self.rfEdit4.setPlainText('')
        regestr = '(.+) 注释行数'
        text_match = re.search(regestr, item.text(), re.DOTALL)
        if text_match:
            id1 = text_match.group(1).strip()
        if not self.caseInfoDic.get(id1):
            self.rfEdit4.setPlainText('未查到用例信息，脚本无效：{}'.format(id1))
            return
        text = self.caseInfoDic[id1]['name'] + '\n' + self.caseInfoDic[id1]['file_path'] + '\n----------------------------------------------------------------------------------------------\n'
        lines1 = self.caseInfoDic[id1]['case_content']
        self.rfEdit4.setPlainText(text)
        self.rfEdit4.set_colored_text(lines1)
        self.statusBar.showMessage(item.text(), 10000)

    def _display_null_case(self, item):
        self.rfEdit4.setPlainText('')
        regestr = '(.+) 脚本行数'
        text_match = re.search(regestr, item.text(), re.DOTALL)
        if text_match:
            id1 = text_match.group(1).strip()
        if not self.caseInfoDic.get(id1):
            self.rfEdit4.setPlainText('未查到用例信息，脚本无效：{}'.format(id1))
            return
        text = self.caseInfoDic[id1]['name'] + '\n' + self.caseInfoDic[id1]['file_path'] + '\n----------------------------------------------------------------------------------------------\n'
        lines1 = self.caseInfoDic[id1]['case_content']
        self.rfEdit4.setPlainText(text)
        self.rfEdit4.set_colored_text(lines1)
        self.statusBar.showMessage(item.text(), 10000)

    def _display_similar_case(self, item):
        self.rfEdit4.setPlainText('')
        regestr = '(.+) 和 (.+) 相似度'
        text_match = re.search(regestr, item.text(), re.DOTALL)
        if text_match:
            id1 = text_match.group(1).strip()
            id2 = text_match.group(2).strip()
        if not self.caseInfoDic.get(id1):
            self.rfEdit4.setPlainText('未查到用例信息，脚本无效：{}'.format(id1))
            return
        if not self.caseInfoDic.get(id2):
            self.rfEdit4.setPlainText('未查到用例信息，脚本无效：{}'.format(id2))
            return
        text = self.caseInfoDic[id1]['name'] + '\n' + self.caseInfoDic[id1]['file_path'] + '\n----------------------------------------------------------------------------------------------\n'
        text = text + '\n' + self.caseInfoDic[id2]['name'] + '\n' + self.caseInfoDic[id2]['file_path'] + '\n----------------------------------------------------------------------------------------------\n'
        lines1 = self.caseInfoDic[id1]['case_content']
        lines2 = self.caseInfoDic[id2]['case_content']
        self.rfEdit4.setPlainText(text)
        text1 = '\n'.join(lines1)
        text2 = '\n'.join(lines2)
        self.rfEdit4.set_colored_diff(text1, text2)
        self.statusBar.showMessage(item.text(), 10000)

    def on_slider_value_change(self):
        self.tempretrue = format(self.sender().value() * 0.1, '.1f')
        self.statusBar.showMessage('tempretrue:   ' + str(self.tempretrue), 10000)

    def on_start_button3_clicked(self):
        self.rfEdit3.clear()
        if self.queryEdit31.text() == '':
            self.on_query_button3_clicked()
        self.startButton3.setEnabled(False)
        self.start_genarate_rf()

    def on_query_button31_clicked(self):
        text = self.queryEdit31.text()
        if not text:
            self.rfEdit3.append('未查到脚本信息')
            return
        caseIdList = text.split(',')
        text = ''
        for caseId in caseIdList:
            case_info = self.caseInfoDic.get(caseId, {})
            if not case_info:
                case_info = self.caseInfoDic0.get(caseId)
            if not case_info:
                text = text = text + '未查到脚本信息\n'
                self.rfEdit3.setPlainText(text)
                return
            text = text + 'RDC路径：' + case_info.get('rdcPath', '') + '\n'
            if not case_info:
                text = text = text + '未查到脚本信息\n'
                self.rfEdit3.setPlainText(text)
                return
            if case_info.get('file_path'):
                path = case_info.get('file_path').split(self.testPathSplitMap.get(self.project, 'testcases'))[1]
                text = text + '脚本路径：' + path +'\n\n'

            text = text + '用例脚本：\n'
            text = text + '*Settings*\n'
            if case_info.get('suite_setup'):
                text = text + 'Suite Setup    ' + case_info.get('suite_setup', []) + '\n'
            if case_info.get('suite_teardown'):
                text = text + 'Suite Teardown    ' + case_info.get('suite_teardown', []) + '\n'
            for v in case_info.get('variables', []):
                text = text + v + '\n'
            for r in case_info.get('resource', []):
                text = text + r + '\n'
            text = text + r + '\n\n'
            text = text + '*Test Cases*' + '\n'
            text = text + case_info.get('name', []) + '\n'
            for c in case_info.get('case_content', []):
                text = text + c + '\n'
            text = text + '\n'
        texts = text.split('\n')
        self.rfEdit3.clear()
        self.rfEdit3.set_colored_text_test_case(texts)


    def start_genarate_rf(self):
        self.statusBar.showMessage('正在生成脚本，这可能花费几分钟时间，请耐心等待...', 120000)
        self.rfEdit3.clear()
        self.rfEdit3.set_colored_text_test_case(['正在生成脚本，这可能花费几分钟时间，请耐心等待...'])
        self.threadAi = AiThread(self)
        self.threadAi.rf_finished.connect(self.on_genarate_rf_finish)
        self.threadAi.daemon = True
        self.threadAi.start()

    def on_genarate_rf_finish(self, para):
        print('生成脚本完毕。')
        text = para.get('result', '')
        print(text)
        if 'AuthFailed' in text:
            text = text + '\n' + 'DN Studio认证失败，请重新登录账号。'
        self.statusBar.showMessage('生成脚本完毕。', 10000)
        self.startButton3.setEnabled(True)
        self.rfEdit3.clear()
        self.rfEdit3.set_colored_text_test_case(text.split('\n'))
        self.save_llm_rf_data(para)

    def save_llm_rf_data(self, para):
        time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        para.update({'account': self.account, 'project': self.project, 'time': time, 'hasScript': self.hasScript})
        print(para)
        self.thread = Thread(target=self._save_llm_rf_data, args=(para,))
        self.thread.daemon = True
        self.thread.start()

    def _save_llm_rf_data(self, para):
        db = MongoDb3()
        db.insert(para, 'RFHelperLLMStatistics')
        db.close()

    def stream_genarate_rf_call_back(self, text):
        self.text_buffer += text

    def init_text_buffer_timer(self):
        from PyQt5.QtCore import QTimer
        self.text_buffer = ""
        self.timer = QTimer()
        self.timer.timeout.connect(self._flush_buffer)
        self.timer.start(1000)

    def _flush_buffer(self):
        if self.text_buffer:
            if self.rfEdit3 is None:
                raise ValueError("Text editor (rfEdit3) is not initialized.")
            self.rfEdit3.moveCursor(self.rfEdit3.textCursor().End)
            self.rfEdit3.insertPlainText(self.text_buffer)
            self.text_buffer = ""

    def on_query_button3_clicked(self):
        db = MongoDb3()
        text = self.queryEdit3.text()
        case_info = db.query({'id': text}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            case_info = db.query({'id0': text}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            self.hasScript = False
        else:
            self.hasScript = True
        self.startButton3.setEnabled(True)
        self.testcaseStepEdit3.setText('查找中。。。')
        self.testcaseNameEdit3.setText('')
        if not text:
            self.testcaseStepEdit3.setText('未查到用例信息，请检查项目设置是否正确。当前项目：{}')
            return
        testName, testStep, testPath = self.get_rdc_test_info(text)
        self.testcasePath = testPath
        self.testcaseNameEdit3.setText(testName)
        if testStep:
            testSteps = testStep.split('\n')
            self.testcaseStepEdit3.setText('')
            self.testcaseStepEdit3.set_colored_text_rdc_step(testSteps)
            if self.devMode == '新手模式' and case_info:
                self.queryEdit31.setText(text)
        else:
            systemId = self.get_rdc_system_id(text)
            if not systemId:
                systemId = self.get_rdc_system_id('TFS_WX_' + text)
            if systemId:
                testName, testStep,  testPath= self.get_rdc_test_info(systemId)
                self.testcaseNameEdit3.setText(testName)
                if testStep:
                    testSteps = testStep.split('\n')
                    self.testcaseStepEdit3.setText('')
                    self.testcaseStepEdit3.set_colored_text_rdc_step(testSteps)
                    if self.devMode == '新手模式' and case_info:
                        self.queryEdit31.setText(text)
                else:
                    self.testcaseStepEdit3.setText('未查到用例信息')
            else:
                self.testcaseStepEdit3.setText('未查到用例信息')
        self.statusBar.showMessage(testPath, 10000)
        self.rfEdit3.setText('')
        self.rfEdit3.setText(f'rdc路径：{testPath}')
        self.on_query_button31_clicked()

    def get_rdc_test_info(self, rdcId):
        try:
            workspace = self.WORK_SPACE_INFO.get(self.project, 'RAN')
            userInfo = {'account': self.account, "token": self.token, 'workspace': workspace}
            testcaseInfo = RdcApi(userInfo).get_item_info(rdcId)
            testPathInfo = RdcApi(userInfo).get_item_path(rdcId)
            if not testcaseInfo or not testPathInfo:
                return '', '', ''
            if not testcaseInfo.get('bo'):
                return '', '', ''
            if len(testcaseInfo['bo'].get('items', [])) > 0:
                testStep = ''
                firstCondition = ''
                for item in testcaseInfo['bo']['items'][0]['fields']:
                    if item['nameEn'] == 'PresetCondition':
                        firstCondition = '预置条件：\n' + item['value'].replace('<p>', '').replace('</p>', '')
                    if item['nameEn'] == "Title":
                        testName = item['value']
                    if item.get('nameEn', '') == "Steps":
                        stepDic = json.loads(item['value'])
                        i = 1
                        for step in stepDic.get('table', []):
                            testStep = testStep + '操作步骤' + str(i) + '：\n' + step['operation'] + '\n'
                            testStep = testStep + '预期结果' + str(i) + '：\n' + step['expect'] + '\n'
                            i = i + 1
                testStep = firstCondition + '\n\n' + testStep
                testPath = testPathInfo['bo']['directories'][rdcId][0]['path']
            else:
                testName = ''
                testStep = ''
                testPath = ''
        except:
            testName = ''
            testStep = ''
            testPath = ''
        return testName, testStep, testPath

    def get_rdc_system_id(self, rdcId):
        workspace = self.WORK_SPACE_INFO.get(self.project, 'RAN')
        userInfo = {'account': self.account, "token": self.token, 'workspace': workspace}
        testcaseInfo = RdcApi(userInfo).get_item_info_with_testcase_id(rdcId)
        systemid = testcaseInfo.get('System_Id')
        return systemid

    def on_item_clicked2(self, item):
        self.item = item
        name = item.text()
        self.keywordName = name
        text = ''
        if self.basicKeywordContentDic.get(name):
            text = f"<h2>基础关键字</h2>\n<p><b>关键字名称：<font color='green'>{self.basicKeywordDic.get(name, {}).get('name', 'null')}</font></b></p><p><b>关键字路径：</b><a href='{self.basicKeywordDic.get(name, {}).get('path', 'null')}'>{self.basicKeywordDic.get(name, {}).get('path', 'null')}</a></p><p><b>引用文件列表：</b></p>"
            for filePath in self.basicKeywordDic.get(name, {}).get('usedFile', {}).get(self.project, []):
                text += f"<li><a href='{filePath}'>{filePath}</a></li>"
        if self.testKeywordContentDic.get(name):
            text = f"<h2>测试套关键字</h2>\n<p><b>关键字名称：<font color='green'>{self.testKeywordDic.get(name, {}).get('name', 'null')}</font></b></p><p><b>关键字路径：</b><a href='{self.testKeywordDic.get(name, {}).get('path', 'null')}'>{self.testKeywordDic.get(name, {}).get('path', 'null')}</a></p><p><b>引用文件列表：</b></p>"
            for filePath in self.testKeywordDic.get(name, {}).get('usedFile', {}).get(self.project, []):
                text += f"<li><a href='{filePath}'>{filePath}</a></li>"
        self.rfEdit2.setHtml(text)
        self.statusBar.showMessage(item.text(), 10000)

    def on_item_anchor_clicked2(self, url):
        file_name = url.toString()
        file_name = urllib.parse.unquote(file_name)
        if len(file_name) > 1 and file_name[1] == ':':
            file_name = file_name[0].upper() + file_name[1:]
        if file_name == 'back_to_index':
            self.on_item_clicked2(self.item)
            return
        fileInfo = self.db.query({'path': file_name}, TEST_CASE_FILE_DB_TABLE)
        file_content = ''
        if fileInfo:
            file_content = fileInfo[0].get('content', '未查到文件内容！')
        elif os.path.exists(file_name):
            file_content = self.get_file_content(file_name)
        else:
            self.rfEdit2.setHtml(f"<h3>文件: {file_name} 未找到</h3>")
            return
        highlighted_content = file_content.replace(self.keywordName,
                f'<span style="background-color: yellow; color: blue;">{self.keywordName}</span>'
            )
        content_html = f"""
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #f8f9fa;
                width: 100%;
                padding: 10px 20px;
                z-index: 1000;
                border-bottom: 1px solid #dee2e6;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            ">
                <a href='back_to_index' style="text-decoration: none; color: #007bff; font-weight: bold;">返回</a>
            </div>
            <div style="margin-top: 80px; padding: 20px;">
                <h3>{file_name}</h3>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px;">{highlighted_content}</pre>
            </div>
            """
        self.rfEdit2.setHtml(content_html)
        self.rfEdit2.move_cursor_to_keyword(self.keywordName)

    def display_in_rfcode(self, path):
        project_explorer = PluginRepository().find('PROJECT_EXPLORER')
        project_explorer.expand_item_by_path(path)
        self.on_item_clicked2(self.item)

    def on_basic_keyword_clicked(self):
        self.on_button2_clicked()

    def on_similar_degree_combobox_clicked(self):
        self.on_button2_clicked()

    def on_button2_clicked(self):
        self.queryResultListWidget2.clear()
        text = self.queryEdit2.text()
        cutoff = float(self.similarDegreeComboBox.currentText())
        resultList = []
        if self.baisKeywordCheckBox.isChecked():
            result = self.find_close_matches(text, self.basicKeywordList, 50, cutoff)
            resultList.extend(result)
        if self.testKeywordCheckBox.isChecked():
            result = self.find_close_matches(text, self.testKeywordList, 50, cutoff)
            resultList.extend(result)
        result = self.find_close_matches(text, resultList, 50, cutoff)
        self.queryResultListWidget2.addItems(result)

    def on_button_clicked(self):
        try:
            testPath = ''
            testName = ''
            self.queryResultListWidget.clear()
            text = self.queryEdit.text().strip()
            isCaseId = bool(re.match(r'^[A-Za-z0-9-]+$', text))
            if isCaseId:
                testName, testStep, testPath = self.get_rdc_test_info(text)
                if not testStep:
                    systemId = self.get_rdc_system_id(text)
                    if not systemId:
                        systemId = self.get_rdc_system_id('TFS_WX_' + text)
                    if systemId:
                        testName, testStep,  testPath= self.get_rdc_test_info(systemId)
            tempDic = {}
            if text != '':
                if testPath:
                    text = testPath + '/' + testName
                testCaseList = self.find_close_matches(text, self.testcaseList, 20, 0.01)
                if not testCaseList:
                    return
                testCaseListShort = []
                for test in testCaseList:
                    testInfo = test.split('__')
                    if len(testInfo) > 4:
                        case_id = testInfo[-1]
                        tempDic[case_id] = test
                        testCaseListShort.append('__'.join(testInfo[1:]))
                self.queryResultListWidget.addItems(testCaseListShort)
        except Exception as e:
            print(str(e))

    def on_button1_clicked(self):
        userInfo = {'account': self.account, "token": self.token}
        try:
            self.queryResultListWidget2.clear()
            text = self.queryEdit1.text()
            tempDic = {}
            if text != '':
                ret = AiStudioApi(userInfo).get_similar_test(text)
                testCase = ret['bo']['result']
                if not testCase:
                    return
                testCaseList = testCase.split('\n\n ')
                testCaseListShort = []
                for test in testCaseList:
                    testInfo = test.split('__')
                    if len(testInfo) > 4:
                        case_id = testInfo[-1]
                        tempDic[case_id] = test
                        testCaseListShort.append('__'.join(testInfo[1:]))
                self.queryResultListWidget2.addItems(testCaseListShort)
        except Exception as e:
            print(str(e))

    def show_context_menu(self, position):
        menu = QMenu()
        open_action = menu.addAction("在RFcode中打开")
        open_action.triggered.connect(self.open_in_rfcode)
        menu.exec_(self.queryResultListWidget.viewport().mapToGlobal(position))

    def open_in_rfcode(self):
        item = self.queryResultListWidget.currentItem()
        if item:
            testInfo = item.text().split('__')
            case_id = testInfo[-1]
            case_info = self.caseInfoDic.get(case_id)
            if not case_info:
                case_info = self.caseInfoDic0.get(case_id)
            if case_info and case_info.get('file_path'):
                project_explorer = PluginRepository().find('PROJECT_EXPLORER')
                project_explorer.expand_item_by_path(case_info['file_path'], case_info['name'])
                project_explorer.update_text_edit()

    def show_context_menu2(self, position):
        menu = QMenu()
        open_action = menu.addAction("在RFcode中打开")
        open_action.triggered.connect(self.open_in_rfcode2)
        menu.exec_(self.queryResultListWidget2.viewport().mapToGlobal(position))

    def open_in_rfcode2(self):
        item = self.queryResultListWidget2.currentItem()
        if item:
            name = item.text()
            path = 'null'
            if self.basicKeywordContentDic.get(name):
                path = self.basicKeywordDic.get(name, {}).get('path', 'null')
            if self.testKeywordContentDic.get(name):
                path = self.testKeywordDic.get(name, {}).get('path', 'null')
            if path != 'null':
                project_explorer = PluginRepository().find('PROJECT_EXPLORER')
                project_explorer.expand_item_by_path(path, name)
                project_explorer.update_text_edit()



    def on_item_clicked(self, item, case_info=None):
        text = ''
        if not case_info:
            self.rfEdit1.setPlainText('')
            testInfo = item.text().split('__')
            case_id = testInfo[-1]
            case_info = self.caseInfoDic.get(case_id)
        else:
            case_info = case_info
        if not case_info:
            case_info = self.caseInfoDic0.get(case_id)
            if not case_info:
                text = '用例脚本未找到'
                self.rfEdit1.setPlainText(text)
                return
        if item:
            text = text + 'RDC路径：' + case_info.get('rdcPath', 'null') + '\n'
            if case_info.get('file_path'):
                print(case_info.get('file_path'))
                path = case_info.get('file_path').split(self.testPathSplitMap.get(self.project, 'testcases'))[1]
                text = text + '脚本路径：' + path +'\n\n'
        text = text + '*Settings*\n'
        if case_info.get('suite_setup'):
            text = text + 'Suite Setup    ' + case_info.get('suite_setup', '') + '\n'
        if case_info.get('suite_teardown'):
            text = text + 'Suite Teardown    ' + case_info.get('suite_teardown', '') + '\n'
        for v in case_info.get('variables', []):
            text = text + v + '\n'
        for r in case_info.get('resource', []):
            text = text + r + '\n'
        text = text  + '\n'
        text = text + '*Test Cases*' + '\n'
        text = text + case_info.get('name', '') + '\n'
        if case_info.get('doc'):
            text = text + '    [Documentation]    '
            docList = case_info.get('doc').split('\n')
            for doc in docList:
                text = text + '    ...    ' + doc + '\n'
        if case_info.get('test_case_setup'):
            text = text + '    [Setup]    '
            text = text + case_info.get('test_case_setup', '') + '\n'
        for c in case_info.get('case_content', []):
            text = text + c + '\n'
        text = text + '\n'
        if case_info.get('test_case_teardown'):
            text = text + '    [Teardown]    '
            text = text + case_info.get('test_case_teardown', '') + '\n'
        keywords = case_info.get('suite_keywords_detail', {}).keys()
        if keywords:
            text = text + '*Keywords*\n'
        for key in keywords:
            if key in text:
                text = text + key + '\n'
                for step in case_info.get('suite_keywords_detail', {})[key]:
                    text = text + step + '\n'
                text = text + '\n'
        texts = text.split('\n')
        if item:
            self.rfEdit1.set_colored_text_test_case(texts)
            self.statusBar.showMessage(item.text(), 10000)
        return text

    def on_choose_directory(self):
        options = QFileDialog.Options()
        self.keywordsDir = QFileDialog.getExistingDirectory(self, "选择目录", "", options=options)
        self.statusBar.showMessage('当前关键字目录：{}'.format(self.keywordsDir), 10000)
        text = '当前关键字目录：{0}\n当前项目目录：{1}\n'.format(self.keywordsDir, self.projectDir)
        self.rfEdit2.setPlainText(text)

    def on_choose_directory2(self):
        options = QFileDialog.Options()
        self.projectDir = QFileDialog.getExistingDirectory(self, "选择目录", "", options=options)
        self.statusBar.showMessage('当前项目目录：{}'.format(self.projectDir), 10000)
        text = '当前关键字目录：{0}\n当前项目目录：{1}\n'.format(self.keywordsDir, self.projectDir)
        self.rfEdit2.setPlainText(text)

    def _update_keywords_info(self):
        self.basicKeywordContentDic = {}
        self.basicKeywordTestcaseDic = {}
        self.testKeywordContentDic = {}
        self.testKeywordTestcaseDic = {}
        self.db = MongoDb3()
        keywordInfo = self.db.query({'project': self.basicKeywordProjectMap.get(self.project, 'Qcell')}, BASIC_KEAYWORDS_DB_TABLE)
        for keyword in keywordInfo:
            if self.basicKeywordDic.get(keyword.get('name')):
                self.basicKeywordContentDic[keyword.get('name')] = keyword.get('content', '')
                self.basicKeywordTestcaseDic[keyword.get('name')] = keyword.get('testcases', '')
        keywordInfo = self.db.query({'project': self.project}, SUITE_KEYWORDS_DB_TABLE)
        for keyword in keywordInfo:
            if self.testKeywordDic.get(keyword.get('name')):
                self.testKeywordContentDic[keyword.get('name')] = keyword.get('content', '')
                self.testKeywordTestcaseDic[keyword.get('name')] = keyword.get('testcases', '')

    def initToolBar(self):
        self.not_login_icon = QApplication.style().standardIcon(QStyle.SP_DialogCloseButton)
        self.login_icon = QApplication.style().standardIcon(QStyle.SP_DialogApplyButton)
        self.open_icon = QApplication.style().standardIcon(QStyle.SP_MessageBoxInformation)
        self.setup_icon = QApplication.style().standardIcon(QStyle.SP_DialogSaveButton)
        tb2 = self.addToolBar("File1")
        self.login_action = QAction(QIcon(self.not_login_icon),"未登录", self)
        setup = QAction(QIcon(self.setup_icon),"设置", self)
        tb2.addAction(self.login_action)
        tb2.addAction(setup)

    def scrollBarPolicy(self):
        return Qt.ScrollBarAlwaysOn

    def find_close_matches(self, word, possibilities, n=30, cutoff=0.4):
        return difflib.get_close_matches(word, possibilities, n=n, cutoff=cutoff)

    def bring_window_to_front(self, window_name: str):
        """
        Bring a window with the given name to the front.

        Args:
            window_name (str): The name of the window to bring to the front.
        """
        pass

    def set_main_workspace_path(self, path):
        """
        Set the main workspace path in the queryEdit33 control.

        Args:
            path (str): The path to set as the main workspace.
        """
        if hasattr(self, 'queryEdit33'):
            self.queryEdit33.setText(path)

    def check_token(self):
        result = (len(self.token) > 20)
        print("result:", result)
        print("token:", self.token)
        if result:
            self.login_action.setIcon(QIcon(self.login_icon))
            self.login_action.setText("已登录")
            print("已登录")
            return True
        else:
            self.login_action.setIcon(QIcon(self.not_login_icon))
            self.login_action.setText("未登录")
            print("未登录")
            return False

    def open_new_console(self, command):
        """
        根据操作系统在新的终端/控制台窗口中执行命令。

        Args:
            command (str or list): 要执行的命令。
        """
        system = platform.system()
        if system == "Windows":
            subprocess.Popen([command], shell=True, creationflags=subprocess.CREATE_NEW_CONSOLE)
        elif system == "Linux":
            result = subprocess.run(f"chmod -R 777 {command}", shell=True, check=True, text=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 启动子进程，捕获标准输出和标准错误
        process = subprocess.Popen(
            [f'./{command}'],  # 替换为你的命令
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True  # Python 3.7+ 可以直接以文本模式读取
        )
        # 流式读取输出
        for line in process.stdout:
            print(line, end='')
        for line in process.stderr:
            print(line, end='')
        # 等待子进程结束
        process.wait()
        sys.exit()

    def on_update_end(self, result):
        if not result:
            if self.aboutDialog:
                self.aboutDialog.updateInfoLabel.setText('已是最新版本')
        if result:
            geometry = self.geometry()
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Information)
            msg.setText("新版本{0}已下载，是否立即重启？".format(result))
            msg.move(geometry.x()+100, geometry.y()+100)
            msg.setWindowTitle("Information")
            msg.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
            res = msg.exec_()
            if res == QMessageBox.Ok:
                if self.aboutDialog:
                    self.aboutDialog.close()
                self.hide()
                try:
                    self.open_new_console(result)
                    print("Program {0} launched successfully.".format(result))
                    self.bring_window_to_front(u"RF开发助手")
                except FileNotFoundError:
                    print("Program {0} not found.".format(result))
                except Exception as e:
                    print("An error occurred: {0}".format(str(e)))

            elif result == QMessageBox.Cancel:
                print("Cancel clicked")
        self.start_load_data()
        self.on_setup_dialog_close({'project': self.project, 'keywordsPath': self.keywordsDir, 'testcasePath': self.projectDir})

    def load_saved_para(self):
        try:
            if os.path.isfile('rfhelper_config.ini'):
                with open('rfhelper_config.ini', 'r') as f:
                    content = f.read()
                    para = eval(content)
                    self.keywordsDir = para.get('keywordsPath')
                    self.projectDir = para.get('testcasePath')
                    self.project = para.get('project', '')
                    print(self.project)
            if os.path.isfile('account.ini'):
                with open('account.ini', 'r') as f:
                    content = f.read()
                    para = eval(content)
                    self.account = para.get('account', '')
                    self.token = para.get('token', '')
        except Exception as e:
            print(e)

    def start_load_data(self):
        self.queryButton.setEnabled(False)
        self.statusBar.showMessage('正在加载数据，请稍后...', 20000)
        self.start_load_data_thread = DatabaseThread(self)
        self.start_load_data_thread.data_loaded.connect(self.on_data_loaded)
        self.start_load_data_thread.daemon = True
        self.start_load_data_thread.start()

    def on_data_loaded(self, data, caseInfoDic, caseInfoDic0):
        self.caseInfoDic = caseInfoDic
        self.caseInfoDic0 = caseInfoDic0
        self.queryButton.setEnabled(True)

    def open_setup_dialog(self, x, y):
        self.dialog = SetupDialogWindow(x, y, {'projectDic': self.projectDic, 'project': self.project, 'keywordsPath': self.keywordsDir, 'testcasePath': self.projectDir})
        self.dialog.setupDialogReady.connect(self.on_setup_dialog_close)
        self.dialog.exec_()

    def open_aboutus_dialog(self, x, y):
        self.aboutDialog = AboutDialog(self, x, y)
        self.aboutDialog.exec_()

    def open_login_dialog(self, x, y):
        self.dialog = LoginDialogWindow(x, y, {'account': self.account})
        self.dialog.loginDialogReady.connect(self.on_login_dialog_close)
        self.dialog.exec_()

    def open_feedback_dialog(self, x, y):
        self.dialog = FeedbackDialog(x, y)
        self.dialog.feedbackDialogReady.connect(self.on_feedback_dialog_close)
        self.dialog.exec_()

    def on_feedback_dialog_close(self, para):
        time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 将评分字典中的数字键转换为字符串键
        if 'score' in para:
            para['score'] = {str(k): v for k, v in para['score'].items()}
        para.update({'account': self.account, 'project': self.project, 'time': time})
        print(para)
        self.thread = Thread(target=self._save_feedback, args=(para,))
        self.thread.daemon = True
        self.thread.start()

    def _save_feedback(self, para):
        db = MongoDb3()
        db.insert(para, 'RFHelperFeedbackStatistic')
        db.close()

    def on_setup_dialog_close(self, para):
        self.project = para.get('project', '')
        print('---on_setup_dialog_close---->', self.project)
        self.start_load_testcase_kb_list()
        self.start_load_data()
        self.start_parser_keywords()
        self.setWindowTitle(f"RF开发助手  (当前领域：{self.project}， 当前用户：{self.account})")
        if 'SSP' in self.project:
            self.tab_widget.setTabText(2, "AI生成脚本")
            self.tab_widget.insertTab(2, self.tab5, 'AI生成脚本(SSP)')
        else:
            self.tab_widget.removeTab(self.tab_widget.indexOf(self.tab5))
            self.tab_widget.insertTab(2, self.tab3, 'AI生成脚本')
        self.tab_widget.setCurrentIndex(0)
        if self.parent:
            self.parent.on_login_dialog_close({'token': self.token, 'account': self.account, 'project': self.project})
        self.rfEdit1.clear()
        self.rfEdit1.set_colored_text_test_case([f'当前领域：{self.project}'])
        self.rfEdit1.set_colored_text_test_case([f'用例路径：{self.TEST_CASE_PATH_INFO.get(self.project)}'])
        self.rfEdit1.set_colored_text_test_case([f'自动化脚本数：{len(self.testcaseList)}'])


    def parser_keywords_and_load_data(self, para):
        self.project = para.get('project', '')
        self.start_load_data()
        self.start_parser_keywords()

    def on_login_dialog_close(self, para):
        self.token = para.get('token', '')
        self.account = para.get('account', '')
        if self.token:
            if self.parent:
                self.parent.on_login_dialog_close(para)
            else:
                self.login_action.setIcon(QIcon(self.login_icon))
                self.login_action.setText("已登录")
                self.statusBar.showMessage('账户{0}登录成功。'.format(self.account), 10000)
                self.setWindowTitle(f"RF开发助手  (当前项目：{self.project}， 当前用户：{self.account})")
        else:
            if self.parent:
                self.parent.on_login_dialog_close(para)
            else:
                self.login_action.setIcon(QIcon(self.not_login_icon))
                self.login_action.setText("未登录")
                self.statusBar.showMessage('账户{0}登录失败。'.format(self.account), 10000)
                self.setWindowTitle(f"RF开发助手  (当前项目：{self.project}， 当前用户：null)")
        print('-------on_login_dialog_close------')

    def start_parser_keywords(self):
        self.statusBar.showMessage('正在解析关键字，请稍后...', 40000)
        self.threadParser = parserKeywordsThread(self)
        self.threadParser.parser_keywords.connect(self.on_parser_keywords_finish)
        self.threadParser.daemon = True
        self.threadParser.start()

    def start_load_testcase_kb_list(self):
        self.threadLoadKb = LoadTestcaseKbThread(self, self.project)
        self.threadLoadKb.Testcase_kb_loaded.connect(self.on_load_testcase_kb_finish)
        self.threadLoadKb.daemon = True
        self.threadLoadKb.start()

    def on_load_testcase_kb_finish(self, testcaseList):
        self.testcaseList = testcaseList
        self.rfEdit1.clear()
        self.rfEdit1.set_colored_text_test_case([f'当前领域：{self.project}'])
        self.rfEdit1.set_colored_text_test_case([f'用例路径：{self.TEST_CASE_PATH_INFO.get(self.project)}'])
        self.rfEdit1.set_colored_text_test_case([f'自动化脚本数：{len(self.testcaseList)}'])

    def on_parser_keywords_finish(self, text):
        print('解析关键字完毕')
        self.statusBar.showMessage('解析关键字完毕。', 40000)
        self.rfEdit2.setPlainText('')
        self.rfEdit2.setPlainText(text)

    def start_keep_connect_thread(self):
        self.threadConnect = Thread(target=self._keep_connect_thread)
        self.threadConnect.daemon = True
        self.threadConnect.start()

    def get_local_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*********", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception as e:
            return "127.0.0.1"

    def _keep_connect_thread(self):
        ip = self.get_local_ip()
        machine = platform.machine()
        architecture = platform.architecture()
        if not self.account:
            self.account = 'null'
        para = {'account': self.account, 'project': self.project, 'ip': ip, 'machine': machine, 'architecture': architecture}
        while True:
            time.sleep(3600)
            now = datetime.datetime.now().strftime('%Y-%m-%d')
            para.update({'time': now})
            db = MongoDb3()
            db.update({'ip': ip, 'time': now}, para, 'RFHelperOnlineStatistics')
            db.close()


def exception_hook(exctype, value, tb):
    print("异常捕获：")
    print(f"类型: {exctype}")
    print(f"值: {value}")
    print("回溯信息:")
    traceback.print_tb(tb)

if __name__ == "__main__":
    sys.excepthook = exception_hook
    app = QApplication(sys.argv)
    demo = RFHelperMainFrame()
    demo.show()
