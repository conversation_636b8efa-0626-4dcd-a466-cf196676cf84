# coding=utf-8
'''
Created on 2024年1月1日

@author: AI Assistant
'''

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QVBoxLayout, QHBoxLayout, QRadioButton, QLabel, QGroupBox

from controller.system_plugin.style.ThemeManager import ThemeManager
from utility.Singleton import Singleton


@Singleton
class Theme():

    def __init__(self):
        self._layout = None
        self._theme_buttons = {}
        self._first_load = False
        self._theme_manager = ThemeManager()

    def load(self):
        self._set_layout()

    def _set_layout(self):
        """设置主题选择布局"""
        self._layout = QVBoxLayout()

        # 创建主题选择组
        theme_group = QGroupBox("选择界面主题")
        theme_layout = QVBoxLayout()

        # 添加说明文字
        description_label = QLabel("选择您喜欢的界面主题，更改将立即生效")
        description_label.setStyleSheet("color: #666666; font-size: 12px; margin-bottom: 10px;")
        theme_layout.addWidget(description_label)

        # 为每个主题创建单选按钮
        themes = self._theme_manager.get_themes()
        for theme_id, theme_info in themes.items():
            radio_button = QRadioButton(theme_info['name'])
            radio_button.setToolTip(theme_info['description'])
            radio_button.toggled.connect(lambda checked, tid=theme_id: self._on_theme_changed(checked, tid))

            # 添加主题描述
            theme_container = QVBoxLayout()
            theme_container.addWidget(radio_button)

            description = QLabel(f"  {theme_info['description']}")
            description.setStyleSheet("color: #888888; font-size: 11px; margin-left: 20px; margin-bottom: 8px;")
            theme_container.addWidget(description)

            theme_layout.addLayout(theme_container)
            self._theme_buttons[theme_id] = radio_button

        theme_group.setLayout(theme_layout)
        self._layout.addWidget(theme_group)

        # 添加弹性空间
        self._layout.addStretch()

        # 设置当前选中的主题
        self._set_current_theme()

    def get_layout(self):
        """获取布局"""
        return self._layout

    def _set_current_theme(self):
        """设置当前选中的主题"""
        self._first_load = True
        try:
            current_theme = self._theme_manager.get_current_theme()

            # 设置对应的单选按钮为选中状态
            if current_theme in self._theme_buttons:
                self._theme_buttons[current_theme].setChecked(True)

        except Exception as e:
            print(f"加载当前主题设置时出错: {e}")
            # 出错时使用默认主题
            if 'light' in self._theme_buttons:
                self._theme_buttons['light'].setChecked(True)
        finally:
            self._first_load = False

    def _on_theme_changed(self, checked, theme_id):
        """主题改变事件处理"""
        if not checked or self._first_load:
            return

        try:
            # 使用主题管理器应用主题
            if self._theme_manager.set_theme(theme_id):
                themes = self._theme_manager.get_themes()
                print(f"主题已切换到: {themes[theme_id]['name']}")

        except Exception as e:
            print(f"切换主题时出错: {e}")
