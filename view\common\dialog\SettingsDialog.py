'''
Created on 2019年11月21日

@author: 10259183
'''
from functools import partial
import sys

from PyQt5.QtWidgets import QVBoxLayout, QMessageBox, QApplication

from controller.system_plugin.edit.view.component.Formator import Fixture, Timeout, Arguments
from model.CurrentItem import CurrentItem
from settings.DialogHelps import get_help
from view.common.dialog.Dialog import Dialog


class SettingsDialog(Dialog):

    def __init__(self, parent, title):
        super().__init__(title)
        self._parent = parent
        self._title = title
        self.resize(700, 100)
        self.description = get_help(title)
        self._add_layout()
        self.setLayout(self.vbox)
        self._line.setCursorPosition(1)
        self._line.setFocus()

    def _add_layout(self):
        line_box, self._line = self._set_line_area()
        comment_box, self._comment_line = self._set_label_line_area('Comment')
        btn_box, self._ok_btn, self._cancel_btn = self._set_btn_area()
        self._ok_btn.clicked.connect(partial(SettingsDialog.fill_data, self))
        self._cancel_btn.clicked.connect(self.close)
        self.vbox = QVBoxLayout()
        for box in (line_box, comment_box,
                    self._set_label_area(self.description), btn_box):
            self.vbox.addLayout(box)

    def set_text(self, data):
        self._line.setText(data[0])
        self._comment_line.setText(data[1])
        self.data = data

    @staticmethod
    def fill_data(self):
        fixture = Fixture(self._line.text(), self._comment_line.text())
        if 'Arguments' in self._title:
            error = self._validate_arguments()
            if not error:
                self._parent.fill_data(fixture.display_value(), force_update=True)
                # 触发数据保存到模型
                self._parent._modify_data()
                self.close()
            self._ok_btn.toggle()
        elif 'Timeout'  in self._title:
            error = self._validate_timeout()
            if not error:
                self._parent.fill_data(self.timeout.display_value(), force_update=True)
                # 触发数据保存到模型
                self._parent._modify_data()
                self.close()
            self._ok_btn.toggle()
        else:
            self._parent.fill_data(fixture.display_value(), force_update=True)
            # 触发数据保存到模型
            self._parent._modify_data()
            self.close()
        if self.data[0] != self._line.text() or self.data[1] != self._comment_line.text():
            self._set_name_with_star()

    def _set_name_with_star(self):
        suite = CurrentItem().get()['suite']
        if suite:
            suite.set_name_with_star()

    def _validate_timeout(self):
        self.timeout = Timeout(self._line.text(), self._comment_line.text())
        error = self.timeout.validate()
        if error:
            QMessageBox.critical(self, 'Validation Error', error, QMessageBox.Ok, QMessageBox.Ok)
        return error

    def _validate_arguments(self):
        self.argument = Arguments(self._line.text())
        error = self.argument.validate()
        if error:
            QMessageBox.critical(self, 'Validation Error', error, QMessageBox.Ok, QMessageBox.Ok)
        return error


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = SettingsDialog('Timeout')
    win.show()
    sys.exit(app.exec_())
