# encoding=utf-8
'''
Created on 2024年12月19日

@author: AI Assistant
'''

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader


class SettingsPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        settings_menu = self._parent_window.addMenu('&设置')
        self._add_item_preferences(settings_menu)

    def _add_item_preferences(self, settings_menu):
        try:
            title = LanguageLoader().get('PREFERENCES')
            print(f"创建偏好设置菜单项，标题: {title}")

            preferences_action = QAction(QIcon(''), title, self._parent_window)

            # 使用lambda确保信号连接正确
            preferences_action.triggered.connect(lambda: self._open_preferences_dialog())

            settings_menu.addAction(preferences_action)
            print("偏好设置菜单项已添加")

        except Exception as e:
            print(f"创建偏好设置菜单项失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)

    def _open_preferences_dialog(self):
        print("=== 偏好设置菜单项被点击 ===")
        try:
            print("步骤1: 开始打开偏好设置对话框...")

            # 直接创建简单的偏好设置对话框
            self._create_preferences_dialog()

        except Exception as e:
            print(f"打开偏好设置对话框失败: {e}")
            import traceback
            traceback.print_exc()

            # 最后的备用方案
            self._create_minimal_dialog()

    def _create_preferences_dialog(self):
        """创建偏好设置对话框"""
        print("步骤2: 尝试导入原始Preferences类...")
        try:
            from controller.system_plugin.tools.item.Preferences.Preferences import Preferences
            print("步骤3: Preferences类导入成功")

            title = LanguageLoader().get('PREFERENCES')
            print(f"步骤4: 对话框标题: {title}")

            self._dialog = Preferences(title)
            print("步骤5: Preferences对象创建成功")

            # 设置窗口置顶
            from PyQt5.QtCore import Qt
            self._dialog.setWindowFlags(self._dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            self._dialog.raise_()
            self._dialog.activateWindow()

            self._dialog.show()
            print("步骤6: 偏好设置对话框已显示并置顶")

        except ImportError as e:
            print(f"步骤3失败: 导入Preferences类失败: {e}")
            self._create_simple_preferences_dialog()
        except Exception as e:
            print(f"步骤4-6失败: 创建或显示对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self._create_simple_preferences_dialog()

    def _create_simple_preferences_dialog(self):
        """创建一个简单的偏好设置对话框作为备用"""
        print("步骤7: 创建简单偏好设置对话框...")
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                         QPushButton, QTabWidget, QWidget, QCheckBox,
                                         QComboBox, QSpinBox, QFormLayout)

            dialog = QDialog(self._parent_window)
            dialog.setWindowTitle("偏好设置")
            dialog.resize(600, 400)
            dialog.setModal(True)

            # 设置窗口置顶
            from PyQt5.QtCore import Qt
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.raise_()
            dialog.activateWindow()

            # 创建主布局
            main_layout = QVBoxLayout()

            # 创建选项卡
            tab_widget = QTabWidget()

            # 语言选项卡
            language_tab = QWidget()
            language_layout = QFormLayout()
            language_combo = QComboBox()
            language_combo.addItems(['中文', 'English'])
            language_layout.addRow("语言:", language_combo)
            language_tab.setLayout(language_layout)
            tab_widget.addTab(language_tab, "语言")

            # 主题选项卡
            theme_tab = QWidget()
            theme_layout = QFormLayout()
            theme_combo = QComboBox()
            theme_combo.addItems(['浅色主题', '深色主题', '深色橙色主题'])
            theme_layout.addRow("主题:", theme_combo)
            theme_tab.setLayout(theme_layout)
            tab_widget.addTab(theme_tab, "主题")

            # 其他选项卡
            other_tab = QWidget()
            other_layout = QFormLayout()
            trace_log_check = QCheckBox("启用跟踪日志")
            font_size_spin = QSpinBox()
            font_size_spin.setRange(8, 24)
            font_size_spin.setValue(12)
            other_layout.addRow("跟踪日志:", trace_log_check)
            other_layout.addRow("字体大小:", font_size_spin)
            other_tab.setLayout(other_layout)
            tab_widget.addTab(other_tab, "其他")

            main_layout.addWidget(tab_widget)

            # 按钮布局
            button_layout = QHBoxLayout()
            ok_btn = QPushButton("确定")
            cancel_btn = QPushButton("取消")
            apply_btn = QPushButton("应用")

            button_layout.addStretch()
            button_layout.addWidget(ok_btn)
            button_layout.addWidget(cancel_btn)
            button_layout.addWidget(apply_btn)

            main_layout.addLayout(button_layout)

            # 连接按钮事件
            ok_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)
            apply_btn.clicked.connect(lambda: print("应用设置"))

            dialog.setLayout(main_layout)
            dialog.show()

            print("步骤8: 简单偏好设置对话框已显示")

        except Exception as e:
            print(f"步骤7-8失败: 创建简单偏好设置对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self._create_minimal_dialog()

    def _create_minimal_dialog(self):
        """创建最小化的对话框作为最后备用"""
        print("步骤9: 创建最小化对话框...")
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton
            from PyQt5.QtCore import Qt

            dialog = QDialog(self._parent_window)
            dialog.setWindowTitle("偏好设置")
            dialog.resize(300, 150)
            dialog.setModal(True)

            # 设置窗口置顶
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.raise_()
            dialog.activateWindow()

            layout = QVBoxLayout()

            label = QLabel("偏好设置对话框\n\n这是一个简化版本的偏好设置对话框。")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)

            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.setLayout(layout)
            dialog.exec_()  # 使用exec_()确保对话框显示

            print("步骤10: 最小化对话框已显示")

        except Exception as e:
            print(f"步骤9-10失败: 创建最小化对话框也失败: {e}")
            import traceback
            traceback.print_exc()
            print("所有偏好设置对话框创建方案都失败了")
