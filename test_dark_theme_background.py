#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试深色主题背景色修复
验证文本编辑器在深色主题下的背景色是否正确显示为深色
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QColor

def test_dark_theme_background():
    """测试深色主题背景色"""
    try:
        from controller.system_plugin.text_edit.TextEditor import TextEditor
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        print("✅ 导入成功")
        
        # 创建文本编辑器
        text_editor = TextEditor()
        print("✅ TextEditor创建成功")
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        print("✅ ThemeManager创建成功")
        
        # 测试深色主题背景色设置
        print(f"\n=== 测试深色主题背景色 ===")
        
        # 设置一些测试内容
        test_content = '''# 这是一个Python文件示例
def hello_world():
    """这是一个函数"""
    print("Hello, World!")
    return True

class TestClass:
    def __init__(self):
        self.value = 42
        
    def get_value(self):
        # 返回值
        return self.value

if __name__ == "__main__":
    test = TestClass()
    hello_world()
'''
        
        # 设置内容
        text_editor.setText(test_content)
        
        # 模拟加载Python文件
        text_editor._file_path = "test.py"
        
        # 设置Python语法高亮
        from iplatform.highlight.PythonHighlighter import PythonHighlighter
        text_editor.lexer = PythonHighlighter(text_editor)
        text_editor.setLexer(text_editor.lexer)
        
        print("✅ Python语法高亮设置完成")
        
        # 应用深色主题
        print(f"\n=== 应用深色主题 ===")
        text_editor._set_theme("dark")
        print("✅ 深色主题应用完成")
        
        # 检查背景色设置
        print(f"\n=== 检查背景色设置 ===")
        
        # 检查编辑器背景色
        paper_color = text_editor.paper()
        print(f"编辑器背景色: {paper_color.name()}")
        
        # 检查文字颜色
        text_color = text_editor.color()
        print(f"编辑器文字颜色: {text_color.name()}")
        
        # 检查调色板
        palette = text_editor.palette()
        base_color = palette.color(palette.Base)
        window_color = palette.color(palette.Window)
        text_palette_color = palette.color(palette.Text)
        
        print(f"调色板Base颜色: {base_color.name()}")
        print(f"调色板Window颜色: {window_color.name()}")
        print(f"调色板Text颜色: {text_palette_color.name()}")
        
        # 检查样式表
        style_sheet = text_editor.styleSheet()
        has_dark_background = "#2B2B2B" in style_sheet
        print(f"样式表包含深色背景: {'✅' if has_dark_background else '❌'}")
        
        # 检查语法高亮器背景色
        if hasattr(text_editor, 'lexer') and text_editor.lexer:
            if hasattr(text_editor.lexer, 'paper'):
                lexer_paper = text_editor.lexer.paper(0)  # 获取默认样式的背景色
                print(f"语法高亮器背景色: {lexer_paper.name()}")
            else:
                print("语法高亮器不支持背景色查询")
        
        # 验证深色主题设置
        print(f"\n=== 验证深色主题设置 ===")
        
        expected_bg = "#2b2b2b"  # 期望的深色背景
        expected_text = "#e0e0e0"  # 期望的浅色文字
        
        bg_correct = paper_color.name().lower() == expected_bg
        text_correct = text_color.name().lower() == expected_text
        palette_bg_correct = base_color.name().lower() == expected_bg
        
        print(f"编辑器背景色正确: {'✅' if bg_correct else '❌'} (期望: {expected_bg}, 实际: {paper_color.name()})")
        print(f"编辑器文字颜色正确: {'✅' if text_correct else '❌'} (期望: {expected_text}, 实际: {text_color.name()})")
        print(f"调色板背景色正确: {'✅' if palette_bg_correct else '❌'} (期望: {expected_bg}, 实际: {base_color.name()})")
        
        # 测试主题切换
        print(f"\n=== 测试主题切换 ===")
        
        # 切换到浅色主题
        text_editor._set_theme("white")
        white_paper = text_editor.paper()
        white_text = text_editor.color()
        print(f"浅色主题 - 背景: {white_paper.name()}, 文字: {white_text.name()}")
        
        # 切换回深色主题
        text_editor._set_theme("dark")
        dark_paper = text_editor.paper()
        dark_text = text_editor.color()
        print(f"深色主题 - 背景: {dark_paper.name()}, 文字: {dark_text.name()}")
        
        # 验证切换后的颜色
        switch_bg_correct = dark_paper.name().lower() == expected_bg
        switch_text_correct = dark_text.name().lower() == expected_text
        
        print(f"切换后背景色正确: {'✅' if switch_bg_correct else '❌'}")
        print(f"切换后文字颜色正确: {'✅' if switch_text_correct else '❌'}")
        
        # 测试主题管理器通知
        print(f"\n=== 测试主题管理器通知 ===")
        
        # 注册文本编辑器到插件仓库
        from utility.PluginRepository import PluginRepository
        plugin_repo = PluginRepository()
        plugin_repo.register('TEXT_EDIT', text_editor)
        
        # 通过主题管理器切换主题
        theme_manager.notify_text_editors('dark')
        manager_paper = text_editor.paper()
        manager_text = text_editor.color()
        
        print(f"主题管理器通知后 - 背景: {manager_paper.name()}, 文字: {manager_text.name()}")
        
        manager_bg_correct = manager_paper.name().lower() == expected_bg
        manager_text_correct = manager_text.name().lower() == expected_text
        
        print(f"主题管理器通知后背景色正确: {'✅' if manager_bg_correct else '❌'}")
        print(f"主题管理器通知后文字颜色正确: {'✅' if manager_text_correct else '❌'}")
        
        # 综合评估
        all_tests_passed = (
            bg_correct and text_correct and palette_bg_correct and
            switch_bg_correct and switch_text_correct and
            manager_bg_correct and manager_text_correct and
            has_dark_background
        )
        
        if all_tests_passed:
            print("\n✅ 深色主题背景色测试全部通过！")
            print("文本编辑器在深色主题下正确显示深色背景")
            print("语法高亮功能正常，不会覆盖主题背景色")
            print("主题切换功能正常工作")
        else:
            print("\n❌ 深色主题背景色测试失败")
            print("需要检查背景色设置逻辑")
        
        return {
            'text_editor': text_editor,
            'theme_manager': theme_manager,
            'all_tests_passed': all_tests_passed,
            'bg_correct': bg_correct,
            'text_correct': text_correct,
            'has_dark_background': has_dark_background
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("深色主题背景色测试")
    main_window.resize(1000, 700)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
深色主题背景色修复测试

问题: 在深色主题下，文本编辑页签的编辑框背景太亮，应该是深色背景

修复内容:
1. 确保深色主题下编辑器背景色为深色 (#2B2B2B)
2. 确保语法高亮器背景色与主题一致
3. 确保调色板设置正确
4. 确保主题切换时背景色正确更新
5. 确保主题管理器通知机制正常工作

测试内容:
- 深色主题背景色设置 (#2B2B2B)
- 深色主题文字颜色设置 (#E0E0E0)
- 语法高亮器背景色一致性
- 主题切换功能
- 主题管理器通知机制

点击按钮开始测试
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试深色主题背景色")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试结果
    test_result = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始深色主题背景色测试 ===")
        
        nonlocal test_result
        test_result = test_dark_theme_background()
        
        if test_result:
            if test_result['all_tests_passed']:
                output_text.append("✅ 深色主题背景色测试通过")
                output_text.append("文本编辑器在深色主题下正确显示深色背景")
                output_text.append("语法高亮功能正常，不会覆盖主题背景色")
                output_text.append("主题切换功能正常工作")
            else:
                output_text.append("❌ 深色主题背景色测试失败")
                output_text.append(f"背景色正确: {'✅' if test_result['bg_correct'] else '❌'}")
                output_text.append(f"文字颜色正确: {'✅' if test_result['text_correct'] else '❌'}")
                output_text.append(f"样式表设置: {'✅' if test_result['has_dark_background'] else '❌'}")
            
            output_text.append("\n=== 测试完成 ===")
            output_text.append("现在可以在应用中切换到深色主题查看效果")
            output_text.append("文本编辑页签应该显示深色背景")
        else:
            output_text.append("❌ 测试执行失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 深色主题背景色测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_dark_theme_background()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
