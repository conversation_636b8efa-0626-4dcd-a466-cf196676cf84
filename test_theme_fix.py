#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试深色主题下表格和输入框样式修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt

def test_theme_fix():
    """测试主题修复功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("深色主题样式修复测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    try:
        # 导入主题管理器
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        
        # 创建切换按钮
        switch_btn = QPushButton("切换到深色主题")
        layout.addWidget(switch_btn)
        
        def switch_theme():
            current_theme = theme_manager.get_current_theme()
            if current_theme == 'light':
                theme_manager.set_theme('dark')
                switch_btn.setText("切换到浅色主题")
            else:
                theme_manager.set_theme('light')
                switch_btn.setText("切换到深色主题")
        
        switch_btn.clicked.connect(switch_theme)
        
        # 测试LineEditArea
        try:
            from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
            
            # 创建测试输入框
            test_line_edit = LineEditArea(main_window, "Arguments")
            test_line_edit.load()
            layout.addLayout(test_line_edit.get_layout())
            
            # 填充一些测试数据
            test_line_edit.fill_data("test argument value")
            
            print("LineEditArea 测试组件已创建")
            
        except Exception as e:
            print(f"创建 LineEditArea 测试失败: {e}")
        
        # 测试Table
        try:
            from controller.system_plugin.edit.view.component.table.Table import Table
            
            # 创建测试表格
            test_table = Table(main_window)
            layout.addWidget(test_table._table)
            
            print("Table 测试组件已创建")
            
        except Exception as e:
            print(f"创建 Table 测试失败: {e}")
        
        # 应用当前主题
        theme_manager.apply_saved_theme()
        
        print(f"当前主题: {theme_manager.get_current_theme()}")
        print("测试窗口已创建，请手动测试主题切换功能")
        
    except Exception as e:
        print(f"测试初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    test_theme_fix()
