#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试表格文字染色和偏好设置响应
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout, QMenuBar, QMenu, QAction)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

def create_test_table_with_colorization():
    """创建带有文字染色的测试表格"""
    table = QTableWidget(10, 5)
    table.setHorizontalHeaderLabels(['关键字', '参数1', '参数2', '参数3', '注释'])
    
    # 添加测试数据
    test_data = [
        ['Log', 'Hello World', '', '', '# 这是注释'],
        ['${variable}', '=', 'Set Variable', 'test_value', ''],
        ['FOR', '${item}', 'IN', '@{list}', ''],
        ['    Log', '${item}', '', '', ''],
        ['END', '', '', '', ''],
        ['Sleep', '1s', '', '', '# 等待1秒'],
        ['Should Be Equal', '${variable}', 'test_value', '', ''],
        ['${result}', '=', 'Get Text', 'xpath=//div', ''],
        ['Comment', '这是一个注释行', '', '', ''],
        ['\\', '继续上一行', '', '', '']
    ]
    
    for row, row_data in enumerate(test_data):
        for col, cell_data in enumerate(row_data):
            item = QTableWidgetItem(cell_data)
            table.setItem(row, col, item)
    
    return table

def apply_manual_colorization(table):
    """手动应用文字染色（模拟Colorizer的效果）"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        is_dark = theme_manager.is_dark_theme(current_theme)
        
        # 根据主题设置颜色
        if is_dark:
            if current_theme == 'dark_orange':
                # 深色橙色主题颜色
                colors = {
                    'text': QColor(225, 225, 225),      # 浅灰色文字
                    'keyword': QColor(102, 217, 239),   # 浅蓝色关键字
                    'variable': QColor(166, 226, 46),   # 浅绿色变量
                    'comment': QColor(117, 113, 94),    # 灰色注释
                    'error': QColor(249, 38, 114),      # 红色错误
                    'background': QColor(50, 50, 50)    # 深灰色背景
                }
            else:  # dark theme
                colors = {
                    'text': QColor(224, 224, 224),      # 浅灰色文字
                    'keyword': QColor(74, 144, 226),    # 蓝色关键字
                    'variable': QColor(152, 195, 121),  # 绿色变量
                    'comment': QColor(128, 128, 128),   # 灰色注释
                    'error': QColor(240, 113, 120),     # 红色错误
                    'background': QColor(64, 64, 64)    # 深灰色背景
                }
        else:
            # 浅色主题颜色
            colors = {
                'text': QColor(0, 0, 0),            # 黑色文字
                'keyword': QColor(25, 105, 225),    # 蓝色关键字
                'variable': QColor(34, 139, 34),    # 绿色变量
                'comment': QColor(192, 192, 192),   # 灰色注释
                'error': QColor(255, 0, 0),         # 红色错误
                'background': QColor(255, 255, 255) # 白色背景
            }
        
        # 应用颜色到表格单元格
        for row in range(table.rowCount()):
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    text = item.text()
                    
                    # 设置背景色
                    item.setBackground(colors['background'])
                    
                    # 根据内容设置前景色
                    if text.startswith('#'):
                        # 注释
                        item.setForeground(colors['comment'])
                    elif text.startswith('${') and text.endswith('}'):
                        # 变量
                        item.setForeground(colors['variable'])
                    elif text in ['Log', 'Sleep', 'Should Be Equal', 'Get Text', 'Set Variable', 'FOR', 'END', 'Comment']:
                        # 关键字
                        item.setForeground(colors['keyword'])
                    elif text == '\\':
                        # 续行符
                        item.setForeground(colors['comment'])
                        item.setBackground(colors['comment'])
                    else:
                        # 普通文字
                        item.setForeground(colors['text'])
        
        print(f"已应用{current_theme}主题的文字染色")
        
    except Exception as e:
        print(f"应用文字染色失败: {e}")
        # 使用默认颜色
        for row in range(table.rowCount()):
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    item.setForeground(QColor(0, 0, 0))
                    item.setBackground(QColor(255, 255, 255))

def test_colorization_settings():
    """测试ColorizationSettings"""
    print("=== 测试ColorizationSettings ===")
    
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
        
        colorization = ColorizationSettings()
        
        print(f"关键字颜色: {colorization.get_keyword_color()}")
        print(f"变量颜色: {colorization.get_variable_color()}")
        print(f"文字颜色: {colorization.get_text_color()}")
        print(f"背景颜色: {colorization.get_background_color()}")
        print(f"注释颜色: {colorization.get_comment_color()}")
        
        # 测试主题切换
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            
            print(f"当前主题: {current_theme}")
            
            # 切换主题并测试颜色变化
            new_theme = 'dark_orange' if current_theme != 'dark_orange' else 'light'
            if theme_manager.set_theme(new_theme):
                print(f"主题已切换到: {new_theme}")
                print(f"切换后关键字颜色: {colorization.get_keyword_color()}")
                print(f"切换后变量颜色: {colorization.get_variable_color()}")
                
                # 切换回原主题
                theme_manager.set_theme(current_theme)
                print(f"已切换回原主题: {current_theme}")
            
        except Exception as e:
            print(f"主题切换测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"ColorizationSettings测试失败: {e}")
        return False

def test_preferences_dialog():
    """测试偏好设置对话框"""
    print("\n=== 测试偏好设置对话框 ===")
    
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        from PyQt5.QtWidgets import QMenuBar
        
        # 创建测试菜单栏
        menu_bar = QMenuBar()
        
        # 创建设置插件
        settings_plugin = SettingsPlugin(menu_bar)
        settings_plugin.load()
        
        print("设置插件加载成功")
        
        # 测试偏好设置对话框
        settings_plugin._open_preferences_dialog()
        print("偏好设置对话框测试完成")
        
        return True
        
    except Exception as e:
        print(f"偏好设置对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("表格文字染色和偏好设置测试")
    main_window.resize(1000, 700)
    
    # 创建菜单栏
    menu_bar = main_window.menuBar()
    
    # 添加设置菜单
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        settings_plugin = SettingsPlugin(menu_bar)
        settings_plugin.load()
        print("✅ 设置菜单已添加")
    except Exception as e:
        print(f"❌ 设置菜单添加失败: {e}")
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
表格文字染色和偏好设置测试

测试内容:
1. 深色主题下表格文字染色（关键字、变量、注释等）
2. 偏好设置对话框响应

测试方法:
1. 点击"应用文字染色"按钮查看表格中的文字颜色
2. 点击"切换主题"按钮测试主题切换时的颜色变化
3. 点击菜单栏"设置" -> "偏好设置"测试对话框响应
4. 点击"测试偏好设置"按钮直接测试对话框
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    colorize_btn = QPushButton("应用文字染色")
    theme_btn = QPushButton("切换主题")
    test_prefs_btn = QPushButton("测试偏好设置")
    test_colorization_btn = QPushButton("测试颜色设置")
    
    button_layout.addWidget(colorize_btn)
    button_layout.addWidget(theme_btn)
    button_layout.addWidget(test_prefs_btn)
    button_layout.addWidget(test_colorization_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_test_table_with_colorization()
    layout.addWidget(QLabel("测试表格 - 观察文字颜色:"))
    layout.addWidget(table)
    
    # 连接按钮事件
    def apply_colorization():
        apply_manual_colorization(table)
        print("文字染色已应用")
    
    def switch_theme():
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current = theme_manager.get_current_theme()
            new_theme = 'dark_orange' if current != 'dark_orange' else 'light'
            if theme_manager.set_theme(new_theme):
                print(f"主题已切换: {current} -> {new_theme}")
                # 重新应用染色
                apply_manual_colorization(table)
            else:
                print("主题切换失败")
        except Exception as e:
            print(f"主题切换失败: {e}")
    
    def test_prefs():
        test_preferences_dialog()
    
    def test_colorization():
        test_colorization_settings()
    
    colorize_btn.clicked.connect(apply_colorization)
    theme_btn.clicked.connect(switch_theme)
    test_prefs_btn.clicked.connect(test_prefs)
    test_colorization_btn.clicked.connect(test_colorization)
    
    # 初始应用染色
    apply_colorization()
    
    # 运行测试
    print("🔍 开始测试...")
    test_colorization_settings()
    test_preferences_dialog()
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
