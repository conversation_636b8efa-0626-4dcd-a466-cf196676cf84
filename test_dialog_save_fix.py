#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试对话框保存修复
验证用户在Setup等对话框中修改内容后，点击确定是否正确保存
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer

def test_dialog_save_functionality():
    """测试对话框保存功能"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        from view.common.dialog.SettingsDialog import SettingsDialog
        
        print("✅ 导入成功")
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建Setup输入框
        setup_area = LineEditArea(parent, 'Setup')
        setup_area.load()
        
        print("✅ LineEditArea创建成功")
        
        # 模拟初始数据
        initial_setup = 'Open Browser    chrome'
        print(f"\n=== 初始数据设置 ===")
        setup_area.fill_data(initial_setup)
        print(f"初始Setup: {initial_setup}")
        
        # 获取初始状态
        initial_data = setup_area.get_data()
        initial_text_state = setup_area._text
        print(f"初始数据: {initial_data}")
        print(f"初始内部状态: {initial_text_state}")
        
        # 模拟用户通过对话框修改数据
        modified_setup = 'Open Browser    firefox    --headless'
        print(f"\n=== 模拟对话框修改 ===")
        print(f"用户修改为: {modified_setup}")
        
        # 模拟对话框的确定按钮操作
        # 1. 调用fill_data with force_update=True
        setup_area.fill_data(modified_setup, force_update=True)
        print("✅ 调用fill_data(force_update=True)完成")
        
        # 2. 调用_modify_data保存到模型
        setup_area._modify_data()
        print("✅ 调用_modify_data()完成")
        
        # 验证修改后的状态
        modified_data = setup_area.get_data()
        modified_text_state = setup_area._text
        print(f"修改后数据: {modified_data}")
        print(f"修改后内部状态: {modified_text_state}")
        
        # 检查数据是否正确更新
        data_updated = (modified_data != initial_data)
        state_updated = (modified_text_state != initial_text_state)
        content_correct = (str(modified_data) == str(modified_setup) or 
                          (modified_data and modified_data[0] == modified_setup))
        
        print(f"\n=== 验证结果 ===")
        print(f"数据已更新: {'✅' if data_updated else '❌'}")
        print(f"内部状态已更新: {'✅' if state_updated else '❌'}")
        print(f"内容正确: {'✅' if content_correct else '❌'}")
        
        # 模拟页签切换，验证数据持久性
        print(f"\n=== 模拟页签切换 ===")
        
        # 模拟系统重新加载相同数据（不应该覆盖用户修改）
        setup_area.fill_data(initial_setup)  # 系统尝试用初始数据重新填充
        print(f"系统尝试重新填充初始数据: {initial_setup}")
        
        # 检查用户修改是否被保持
        final_data = setup_area.get_data()
        final_text_state = setup_area._text
        print(f"最终数据: {final_data}")
        print(f"最终内部状态: {final_text_state}")
        
        # 验证用户修改是否被保持
        modification_preserved = (final_text_state == modified_setup)
        print(f"用户修改被保持: {'✅' if modification_preserved else '❌'}")
        
        # 测试force_update功能
        print(f"\n=== 测试force_update功能 ===")
        test_data = 'Test Browser    edge'
        setup_area.fill_data(test_data, force_update=True)
        force_update_data = setup_area.get_data()
        force_update_works = (str(force_update_data) == str(test_data) or 
                             (force_update_data and force_update_data[0] == test_data))
        print(f"force_update功能正常: {'✅' if force_update_works else '❌'}")
        
        all_tests_passed = (data_updated and state_updated and content_correct and 
                           modification_preserved and force_update_works)
        
        if all_tests_passed:
            print("\n✅ 所有测试通过，对话框保存功能修复成功！")
        else:
            print("\n❌ 部分测试失败，需要进一步修复")
        
        return {
            'setup_area': setup_area,
            'all_tests_passed': all_tests_passed,
            'data_updated': data_updated,
            'state_updated': state_updated,
            'content_correct': content_correct,
            'modification_preserved': modification_preserved,
            'force_update_works': force_update_works
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("对话框保存修复测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
对话框保存修复测试

问题: 在编辑页签修改Setup文本框内容后点确定，然后切换到文本编辑再返回编辑页签，
Setup文本框的内容又被重置了，编辑的内容未保存

根本原因: 
1. SettingsDialog的确定按钮只调用了fill_data()更新显示
2. 没有调用_modify_data()保存数据到模型中
3. fill_data()的数据检查阻止了对话框的强制更新

修复: 
1. 在SettingsDialog中添加_modify_data()调用
2. 在LineEditArea.fill_data()中添加force_update参数
3. 对话框调用时使用force_update=True跳过数据检查

测试: 点击按钮测试对话框保存功能
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试对话框保存功能")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试结果
    test_result = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始对话框保存测试 ===")
        
        nonlocal test_result
        test_result = test_dialog_save_functionality()
        
        if test_result:
            if test_result['all_tests_passed']:
                output_text.append("✅ 对话框保存测试通过")
                output_text.append("用户在对话框中的修改能正确保存到模型")
                output_text.append("页签切换后修改内容保持不变")
                output_text.append("force_update功能正常工作")
            else:
                output_text.append("❌ 对话框保存测试失败")
                output_text.append(f"数据更新: {'✅' if test_result['data_updated'] else '❌'}")
                output_text.append(f"状态更新: {'✅' if test_result['state_updated'] else '❌'}")
                output_text.append(f"内容正确: {'✅' if test_result['content_correct'] else '❌'}")
                output_text.append(f"修改保持: {'✅' if test_result['modification_preserved'] else '❌'}")
                output_text.append(f"强制更新: {'✅' if test_result['force_update_works'] else '❌'}")
            
            output_text.append("\n=== 测试完成 ===")
            output_text.append("如果测试通过，说明对话框保存功能修复成功")
        else:
            output_text.append("❌ 测试执行失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 对话框保存修复测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_dialog_save_functionality()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
