#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试关键字背景色修复
验证LineEditArea中关键字的背景色是否正确设置
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt

def test_keyword_background_colors():
    """测试关键字背景色设置"""
    print("=== 测试关键字背景色设置 ===")
    
    try:
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 模拟LineEditArea的关键字颜色设置逻辑
        def simulate_keyword_color_setting(current_theme, text, is_keyword=True):
            if is_keyword:
                # 模拟关键字颜色 (蓝色)
                color = (25, 105, 225)  # 默认关键字颜色
                
                if current_theme == 'dark':
                    bg_color = "#353535" if text else "#2A2A2A"
                elif current_theme == 'dark_orange':
                    bg_color = "#3A3A3A" if text else "#2D2D2D"
                else:
                    bg_color = "#ffffff" if text else "#c0c0c0"
                
                keyword_color = f"rgb({color[0]}, {color[1]}, {color[2]})"
                style = f"background-color: {bg_color}; color: {keyword_color};"
                return style
            else:
                # 非关键字使用普通颜色设置
                if current_theme == 'dark':
                    bg_color = "#353535" if text else "#2A2A2A"
                    text_color = "#E0E0E0"
                elif current_theme == 'dark_orange':
                    bg_color = "#3A3A3A" if text else "#2D2D2D"
                    text_color = "#b1b1b1"
                else:
                    bg_color = "#ffffff" if text else "#c0c0c0"
                    text_color = "#000000"
                
                style = f"background-color: {bg_color}; color: {text_color};"
                return style
        
        # 测试不同主题和内容组合
        themes = ['light', 'dark', 'dark_orange']
        test_cases = [
            ('Log', True),      # 关键字，有内容
            ('', True),         # 关键字，无内容
            ('normal text', False),  # 非关键字，有内容
            ('', False),        # 非关键字，无内容
        ]
        
        for theme in themes:
            print(f"\n--- 主题: {theme} ---")
            
            for text, is_keyword in test_cases:
                style = simulate_keyword_color_setting(theme, text, is_keyword)
                content_type = "关键字" if is_keyword else "普通文本"
                content_status = "有内容" if text else "无内容"
                
                print(f"{content_type}({content_status}): {style}")
                
                # 验证样式格式
                has_background = 'background-color:' in style
                has_color = 'color:' in style
                no_white_bg = 'background-color: #ffffff' not in style or theme == 'light'
                
                print(f"  - 包含背景色: {'✅' if has_background else '❌'}")
                print(f"  - 包含文字颜色: {'✅' if has_color else '❌'}")
                print(f"  - 背景色正确: {'✅' if no_white_bg else '❌'}")
        
        print("\n=== 测试HTML vs 纯文本设置 ===")
        
        # 对比HTML设置和纯文本设置
        html_style = '<a href = "Log"><font style="color:rgb(25, 105, 225);">Log</font></a> To Console    ${message}'
        plain_style = 'background-color: #353535; color: rgb(25, 105, 225);'
        
        print(f"HTML设置: {html_style}")
        print(f"纯文本设置: {plain_style}")
        print("HTML设置问题: 使用font标签会覆盖背景色")
        print("纯文本设置优势: 通过CSS样式表控制背景色和文字颜色")
        
        app.quit()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_colorizer_integration():
    """测试Colorizer集成"""
    print("\n=== 测试Colorizer集成 ===")
    
    try:
        # 模拟Colorizer._is_keyword方法
        def mock_is_keyword(keyword):
            common_keywords = ['Log', 'Set Variable', 'Should Be Equal', 'Click Element']
            return keyword.strip() in common_keywords
        
        test_keywords = [
            'Log',
            'Set Variable', 
            'Should Be Equal',
            'Click Element',
            'Custom Keyword',
            'normal text'
        ]
        
        for keyword in test_keywords:
            is_keyword = mock_is_keyword(keyword)
            print(f"'{keyword}' -> 是关键字: {'✅' if is_keyword else '❌'}")
        
    except Exception as e:
        print(f"测试Colorizer集成时出错: {e}")

def main():
    """主测试函数"""
    print("开始测试关键字背景色修复...")
    
    try:
        test_keyword_background_colors()
        test_colorizer_integration()
        
        print("\n=== 修复总结 ===")
        print("1. 移除了HTML格式的文本设置，避免<font>标签覆盖背景色")
        print("2. 使用setPlainText()设置纯文本内容")
        print("3. 通过setStyleSheet()统一设置背景色和文字颜色")
        print("4. 确保关键字和非关键字都使用一致的背景色")
        print("5. 保持主题切换时的颜色一致性")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
