#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文档布局更新修复
验证 "native Qt signal is not callable" 错误是否已修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer

def test_line_edit_area():
    """测试LineEditArea是否正常工作"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建LineEditArea实例
        area = LineEditArea(parent, 'Test Area')
        area.load()
        
        print("✅ LineEditArea创建成功")
        
        # 测试填充数据，这会触发垂直居中功能
        test_data = [
            "测试数据1",
            "${variable}",
            "Close Browser",
            "Log    Hello World",
            "Sleep    2s"
        ]
        
        for i, data in enumerate(test_data):
            try:
                area.fill_data(data)
                print(f"✅ 第{i+1}次数据填充成功: {data}")
            except Exception as e:
                print(f"❌ 第{i+1}次数据填充失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("✅ 所有测试完成，没有出现 'native Qt signal is not callable' 错误")
        
        return area
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("文档布局更新修复测试")
    main_window.resize(700, 500)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
文档布局更新修复测试

问题: 出现 "native Qt signal is not callable" 错误
原因: documentLayout().update() 调用错误，update() 是Qt信号不能直接调用

修复: 
1. 将 documentLayout().update() 改为 widget.update()
2. 使用正确的重绘方法

测试: 点击按钮测试LineEditArea的垂直居中功能是否正常工作
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试LineEditArea垂直居中")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试LineEditArea
    test_area = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始测试 ===")
        
        nonlocal test_area
        test_area = test_line_edit_area()
        
        if test_area:
            output_text.append("✅ LineEditArea创建成功")
            
            # 测试多次填充数据，每次都会触发垂直居中
            test_data = [
                "测试数据1",
                "${variable}",
                "Close Browser", 
                "Log    Hello World",
                "Sleep    2s",
                "FOR    ${item}    IN    @{list}",
                "END"
            ]
            
            for i, data in enumerate(test_data):
                try:
                    test_area.fill_data(data)
                    output_text.append(f"✅ 第{i+1}次数据填充成功: {data}")
                    # 添加小延迟，让用户看到进度
                    QTimer.singleShot(100 * (i + 1), lambda: None)
                except Exception as e:
                    output_text.append(f"❌ 第{i+1}次数据填充失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            output_text.append("=== 测试完成 ===")
            output_text.append("如果没有出现 'native Qt signal is not callable' 错误，说明修复成功")
        else:
            output_text.append("❌ LineEditArea创建失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 文档布局更新修复测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_line_edit_area()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
