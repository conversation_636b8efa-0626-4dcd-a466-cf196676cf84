#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的主题样式测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QTextEdit, QLabel, QHBoxLayout)
from PyQt5.QtCore import Qt

def create_test_table():
    """创建测试表格"""
    table = QTableWidget(5, 3)
    table.setHorizontalHeaderLabels(['Column 1', 'Column 2', 'Column 3'])
    
    # 添加一些测试数据
    for row in range(5):
        for col in range(3):
            item = QTableWidgetItem(f"Item {row},{col}")
            table.setItem(row, col, item)
    
    return table

def create_test_input():
    """创建测试输入框"""
    layout = QHBoxLayout()
    
    label = QLabel("Arguments")
    label.setFixedWidth(170)
    
    text_edit = QTextEdit()
    text_edit.setFixedHeight(25)
    text_edit.setPlainText("test argument value")
    
    clear_btn = QPushButton("Clear")
    clear_btn.setFixedSize(120, 22)
    
    layout.addWidget(label)
    layout.addWidget(text_edit)
    layout.addWidget(clear_btn)
    
    return layout, text_edit

def apply_dark_theme_styles(text_edit):
    """手动应用深色主题样式到输入框"""
    text_edit.setStyleSheet("background-color: #404040; color: #E0E0E0;")

def apply_light_theme_styles(text_edit):
    """手动应用浅色主题样式到输入框"""
    text_edit.setStyleSheet("background-color: #ffffff; color: #000000;")

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("主题样式测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试深色主题下的表格行列编号和输入框背景色")
    layout.addWidget(info_label)
    
    # 创建主题切换按钮
    theme_btn = QPushButton("切换到深色主题")
    layout.addWidget(theme_btn)
    
    # 创建测试表格
    table = create_test_table()
    layout.addWidget(table)
    
    # 创建测试输入框
    input_layout, text_edit = create_test_input()
    layout.addLayout(input_layout)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("切换到深色主题")
                apply_light_theme_styles(text_edit)
                is_dark = False
                print("已切换到浅色主题")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("切换到浅色主题")
                apply_dark_theme_styles(text_edit)
                is_dark = True
                print("已切换到深色主题")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                main_window.setStyleSheet("")
                apply_light_theme_styles(text_edit)
                theme_btn.setText("切换到深色主题")
                is_dark = False
            else:
                # 手动应用深色样式
                dark_style = """
                QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                QTableWidget { 
                    background-color: #404040; 
                    color: #E0E0E0; 
                    gridline-color: #555555; 
                }
                QHeaderView::section { 
                    background-color: #3C3C3C; 
                    color: #E0E0E0; 
                    border: 1px solid #555555; 
                }
                QPushButton { 
                    background-color: #404040; 
                    color: #E0E0E0; 
                    border: 1px solid #555555; 
                }
                QLabel { color: #E0E0E0; }
                """
                main_window.setStyleSheet(dark_style)
                apply_dark_theme_styles(text_edit)
                theme_btn.setText("切换到浅色主题")
                is_dark = True
    
    theme_btn.clicked.connect(toggle_theme)
    
    print("测试窗口已创建")
    print("请点击按钮切换主题，观察表格行列编号和输入框背景色的变化")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
