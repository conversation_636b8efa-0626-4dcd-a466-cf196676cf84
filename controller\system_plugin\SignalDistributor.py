# coding=utf-8
'''
Created on 2019年11月26日

@author: 10240349
'''
from PyQt5.Qt import QObject, pyqtSignal

from model.CurrentItem import CurrentItem
from utility.PluginRepository import PluginRepository
from utility.Singleton import Singleton
from utility.UIRepository import UIRepository


@Singleton
class SignalDistributor(QObject):

    text_editor_modify_item = pyqtSignal(object)
    editor_modify_item = pyqtSignal(object, dict)
    show_item = pyqtSignal(object, bool)
    highlight_keyword = pyqtSignal(str)
    text_editor_save_item = pyqtSignal(object, dict)
    text_editor_update_item = pyqtSignal(object, dict)
    update_item = pyqtSignal(object, object)
    star_modify = pyqtSignal(object)
    modify_table = pyqtSignal()
    format_save_item = pyqtSignal()
    global_save_content = pyqtSignal()
    refresh_text_edit = pyqtSignal(object)
    modify_table_item = pyqtSignal(str)
    all_item_save = pyqtSignal()
    bash_load = pyqtSignal()
    rf_assistant_load = pyqtSignal()
    rf_assistant_unload = pyqtSignal()
    close_event = pyqtSignal()
    locate_varible = pyqtSignal(int)

    def highlight_selected_keyword(self, keyword):
        self.highlight_keyword.emit(keyword)

    def text_editor_modify(self, view_item):
        self.text_editor_modify_item.emit(view_item,)

    def editor_modify(self, view_item, data_dict):
        self.editor_modify_item.emit(view_item, data_dict)

    def modify_table_event(self):
        self.modify_table.emit()

    def modify_table_item_event(self, text):
        self.modify_table_item.emit(text)

    def start_bash_load(self):
        self.bash_load.emit()

    def show(self, view_item, is_add=False):
        text_edit = PluginRepository().find('TEXT_EDIT')
        if CurrentItem().name:
            text_edit.set_last_file_path(CurrentItem().get().get("path"))
        CurrentItem().set(view_item)
        if not UIRepository().find('project_tree_items'):
            UIRepository().update('project_tree_items', [CurrentItem().get_current_item()])
        elif UIRepository().find('project_tree_items')[-1] != CurrentItem().get_current_item():
            UIRepository().update('project_tree_items', UIRepository().find('project_tree_items') +
                                  [CurrentItem().get_current_item()])
        self.show_item.emit(view_item, is_add)

    def text_editor_save(self, item, modified_values):
        self.text_editor_save_item.emit(item, modified_values)

    def text_editor_update(self, item, modified_values):
        self.text_editor_update_item.emit(item, modified_values)

    def update(self, item, parent):
        self.update_item.emit(item, parent)

    def del_star_modify(self, item):
        self.star_modify.emit(item)

    def format_save(self):
        self.format_save_item.emit()

    def global_save(self):
        self.global_save_content.emit()

    def refresh_text_edit_content(self, item):
        self.refresh_text_edit.emit(item)

    def save_all(self):
        self.all_item_save.emit()

    def emit_close_event(self):
        self.close_event.emit()

    def emit_varible_locate(self, index):
        self.locate_varible.emit(index)
