#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试语法高亮功能
验证文本编辑器的语法高亮是否正常工作，并且不覆盖主题背景色
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer

def test_syntax_highlighting():
    """测试语法高亮功能"""
    try:
        from controller.system_plugin.text_edit.TextEditor import TextEditor
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        print("✅ 导入成功")
        
        # 创建文本编辑器
        text_editor = TextEditor()
        print("✅ TextEditor创建成功")
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        print("✅ ThemeManager创建成功")
        
        # 测试Python文件语法高亮
        print(f"\n=== 测试Python语法高亮 ===")
        
        # 创建一个临时的Python文件内容
        python_content = '''# 这是一个Python文件示例
def hello_world():
    """这是一个函数"""
    print("Hello, World!")
    return True

class TestClass:
    def __init__(self):
        self.value = 42
        
    def get_value(self):
        # 返回值
        return self.value

if __name__ == "__main__":
    test = TestClass()
    hello_world()
'''
        
        # 设置Python内容
        text_editor.setText(python_content)
        
        # 模拟加载Python文件
        text_editor._file_path = "test.py"
        text_editor.lexer = None
        
        # 测试Python语法高亮设置
        from iplatform.highlight.PythonHighlighter import PythonHighlighter
        text_editor.lexer = PythonHighlighter(text_editor)
        text_editor.setLexer(text_editor.lexer)
        text_editor._apply_syntax_highlighting_for_theme()
        
        print("✅ Python语法高亮设置完成")
        
        # 测试Robot文件语法高亮
        print(f"\n=== 测试Robot语法高亮 ===")
        
        robot_content = '''*** Settings ***
Documentation    这是一个Robot Framework测试套件
Library          SeleniumLibrary

*** Variables ***
${URL}           https://example.com
${BROWSER}       chrome

*** Test Cases ***
打开网页测试
    [Documentation]    测试打开网页功能
    [Tags]            smoke
    Open Browser    ${URL}    ${BROWSER}
    Title Should Be    Example Domain
    Close Browser

*** Keywords ***
自定义关键字
    [Documentation]    这是一个自定义关键字
    Log    执行自定义关键字
'''
        
        # 设置Robot内容
        text_editor.setText(robot_content)
        
        # 模拟加载Robot文件
        text_editor._file_path = "test.robot"
        text_editor.lexer = None
        
        # 测试Robot语法高亮设置
        from iplatform.highlight.RobotHighlighter import RobotHighlighter
        text_editor.lexer = RobotHighlighter(text_editor)
        text_editor.setLexer(text_editor.lexer)
        text_editor._apply_syntax_highlighting_for_theme()
        
        print("✅ Robot语法高亮设置完成")
        
        # 测试主题切换
        print(f"\n=== 测试主题切换 ===")
        
        # 测试浅色主题
        text_editor._set_theme("white")
        print("✅ 浅色主题应用完成")
        
        # 测试深色主题
        text_editor._set_theme("dark")
        print("✅ 深色主题应用完成")
        
        # 测试主题管理器通知
        print(f"\n=== 测试主题管理器通知 ===")
        
        # 注册文本编辑器到插件仓库（模拟）
        from utility.PluginRepository import PluginRepository
        plugin_repo = PluginRepository()
        plugin_repo.register('TEXT_EDIT', text_editor)
        
        # 测试主题管理器通知
        theme_manager.notify_text_editors('light')
        print("✅ 浅色主题通知完成")
        
        theme_manager.notify_text_editors('dark')
        print("✅ 深色主题通知完成")
        
        # 验证语法高亮器状态
        print(f"\n=== 验证语法高亮器状态 ===")
        
        has_lexer = hasattr(text_editor, 'lexer') and text_editor.lexer is not None
        print(f"语法高亮器存在: {'✅' if has_lexer else '❌'}")
        
        if has_lexer:
            lexer_type = type(text_editor.lexer).__name__
            print(f"语法高亮器类型: {lexer_type}")
            
            has_color_method = hasattr(text_editor.lexer, 'setColor')
            print(f"支持颜色设置: {'✅' if has_color_method else '❌'}")
            
            has_paper_method = hasattr(text_editor.lexer, 'setPaper')
            print(f"支持背景设置: {'✅' if has_paper_method else '❌'}")
        
        # 验证主题应用方法
        print(f"\n=== 验证主题应用方法 ===")
        
        has_theme_methods = (
            hasattr(text_editor, '_apply_syntax_highlighting_for_theme') and
            hasattr(text_editor, '_apply_dark_syntax_highlighting') and
            hasattr(text_editor, '_apply_light_syntax_highlighting')
        )
        print(f"主题应用方法存在: {'✅' if has_theme_methods else '❌'}")
        
        has_theme_notification = hasattr(text_editor, '_apply_theme_from_manager')
        print(f"主题通知方法存在: {'✅' if has_theme_notification else '❌'}")
        
        all_tests_passed = has_lexer and has_theme_methods
        
        if all_tests_passed:
            print("\n✅ 所有语法高亮测试通过！")
            print("语法高亮功能已启用，支持Python和Robot Framework")
            print("语法高亮背景色透明，不会覆盖主题背景色")
            print("支持主题切换时自动调整语法高亮颜色")
        else:
            print("\n❌ 部分语法高亮测试失败")
        
        return {
            'text_editor': text_editor,
            'theme_manager': theme_manager,
            'all_tests_passed': all_tests_passed,
            'has_lexer': has_lexer,
            'has_theme_methods': has_theme_methods,
            'has_theme_notification': has_theme_notification
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("语法高亮功能测试")
    main_window.resize(1000, 700)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
语法高亮功能测试

功能: 为文本编辑器启用语法高亮功能，支持Python和Robot Framework文件

特性:
1. 根据文件扩展名自动选择语法高亮器
2. 支持主题切换时自动调整语法高亮颜色
3. 语法高亮背景色透明，不覆盖主题背景色
4. 注释部分使用合适的颜色显示

测试内容:
- Python语法高亮（关键字、字符串、注释、函数等）
- Robot Framework语法高亮（关键字、变量、注释等）
- 浅色主题下的语法高亮颜色
- 深色主题下的语法高亮颜色
- 主题管理器通知机制

点击按钮开始测试
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试语法高亮功能")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试结果
    test_result = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始语法高亮功能测试 ===")
        
        nonlocal test_result
        test_result = test_syntax_highlighting()
        
        if test_result:
            if test_result['all_tests_passed']:
                output_text.append("✅ 语法高亮功能测试通过")
                output_text.append("语法高亮已启用，支持Python和Robot Framework")
                output_text.append("语法高亮背景色透明，不覆盖主题背景色")
                output_text.append("支持主题切换时自动调整语法高亮颜色")
                output_text.append("注释部分会以合适的颜色显示")
            else:
                output_text.append("❌ 语法高亮功能测试失败")
                output_text.append(f"语法高亮器: {'✅' if test_result['has_lexer'] else '❌'}")
                output_text.append(f"主题方法: {'✅' if test_result['has_theme_methods'] else '❌'}")
                output_text.append(f"主题通知: {'✅' if test_result['has_theme_notification'] else '❌'}")
            
            output_text.append("\n=== 测试完成 ===")
            output_text.append("现在可以在文本编辑器中看到语法高亮效果")
            output_text.append("注释行会以灰色或绿色显示，关键字会以蓝色显示")
        else:
            output_text.append("❌ 测试执行失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 语法高亮功能测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_syntax_highlighting()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
