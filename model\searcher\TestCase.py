'''
Created on 2019年12月18日

@author: 10243352
'''
import os
import time

import chardet

from controller.parser.reader.ReaderFactory import ReaderFactory
from model.data_file.env import SUPPORT_SUITE_FORMAT
from model.searcher.repositories.TestCasesRepository import TestCasesRepository
from utility.log.SystemLogger import logger


class TestCase(object):
    CACHE = {}

    def __init__(self, path, file_counter):
        self._clear()
        self._path = path
        if self._verify_path():
            return
        for root, _, files in os.walk(path):
            for file in files:
                file_counter.add_number_of_analyzed_files(1)
                file_path = os.path.abspath(os.path.join(root, file))
                if file_path in TestCase.CACHE:
                    if self._get_record_modify_time(file_path) == self._get_latest_modify_time(file_path):
                        continue
                TestCase.CACHE[file_path] = self._get_latest_modify_time(file_path)
                TestCasesRepository().delete(file_path)
                if os.path.splitext(file_path)[-1] not in SUPPORT_SUITE_FORMAT:
                    continue
                self._store_testcases(file_path)
        file_counter.set_analyzed_finished()

    def search(self, key):
        results = []
        if self._verify_path():
            return results
        for path in TestCasesRepository().keys():
            names = TestCasesRepository().find(path)
            match_names = filter(lambda testcase_name: key in testcase_name and path.startswith(os.path.abspath(self._path + os.path.sep)), names)
            sub_results = [[name, path] for name in match_names]
            results.extend(sub_results)
        return results

    def _get_latest_modify_time(self, path):
        return time.mktime(time.localtime(os.stat(path).st_mtime))

    def _get_record_modify_time(self, path):
        return TestCase.CACHE[path]

    def _clear(self):
        self._clear_repo()
        self._clear_cache()

    def _clear_cache(self):
        cache_keys = [path for path in TestCase.CACHE.keys()]
        for path in cache_keys:
            if not os.path.exists(path):
                del TestCase.CACHE[path]

    def _clear_repo(self):
        repo_keys = [path for path in TestCasesRepository().keys()]
        for path in repo_keys:
            if not os.path.exists(path):
                TestCasesRepository().delete(path)

    def _store_testcases(self, file_path):
        with open(file_path, "rb") as file_handler:
            begin_record = False
            line = file_handler.readline()
            encode_type = None
            while(line):
                line = file_handler.readline()
                try:
                    encode_type = self._get_encode_type(line, encode_type)
                    line = line.decode(encoding=encode_type)
                except Exception as _:
                    logger.info("decode fail\ndecode file is: %s.\ndecode line is: %s." % (file_path, line))
                    continue
                tag = line.rstrip().strip("*").lower()
                if not begin_record and line.startswith('*') and tag.strip() in ["test case", "testcase", "testcases", "test cases"]:
                    begin_record = True
                    continue
                if begin_record and line.startswith('*') and tag.strip() in ["settings", "variables", "keywords", "keyword"]:
                    break
                if not begin_record:
                    continue
                self._store_testcase(file_path, line)

    def _store_testcase(self, file_path, line_content):
        cells = line_content.split(ReaderFactory.get_instance(file_path).CELL_SEPERATOR)
        first_cell = cells[0].strip()
        if first_cell != "" and not first_cell.startswith("#"):
            TestCasesRepository().add(file_path, cells[0])

    def _get_encode_type(self, line, encode_type):
        if encode_type is None:
            encode_type = chardet.detect(line).get("encoding")
            encode_type = encode_type if encode_type != "ascii" else "utf-8"
            return encode_type
        return encode_type

    def _verify_path(self):
        return not os.path.exists(self._path)

if __name__ == '__main__':
    time_01 = time.time()
    results = TestCase(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test\testcases").search("小区")
    time_02 = time.time()
    print("11111111111", time_02 - time_01)
    print(TestCase(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test\testcases").search("小区"))
    time_03 = time.time()
    print("11111111111", time_03 - time_02)
