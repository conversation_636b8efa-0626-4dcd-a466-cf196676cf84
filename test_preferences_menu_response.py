#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专门测试偏好设置菜单项点击响应
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QLabel, QHBoxLayout, QMenuBar, QTextEdit)
from PyQt5.QtCore import Qt, QTimer
import io
import contextlib

class OutputCapture:
    """捕获print输出"""
    def __init__(self):
        self.output = []
    
    def write(self, text):
        self.output.append(text)
        print(text, end='')  # 同时输出到控制台
    
    def flush(self):
        pass
    
    def get_output(self):
        return ''.join(self.output)

def test_settings_plugin_creation():
    """测试SettingsPlugin的创建和加载"""
    print("=== 测试SettingsPlugin创建 ===")
    
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        from PyQt5.QtWidgets import QMenuBar
        
        # 创建测试菜单栏
        menu_bar = QMenuBar()
        
        # 创建设置插件
        settings_plugin = SettingsPlugin(menu_bar)
        print("✅ SettingsPlugin对象创建成功")
        
        # 加载菜单
        settings_plugin.load()
        print("✅ SettingsPlugin菜单加载成功")
        
        # 检查菜单是否存在
        menus = menu_bar.findChildren(menu_bar.__class__)
        print(f"菜单栏中的菜单数量: {len(menu_bar.actions())}")
        
        for action in menu_bar.actions():
            print(f"菜单项: {action.text()}")
        
        return settings_plugin, menu_bar
        
    except Exception as e:
        print(f"❌ SettingsPlugin创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_preferences_menu_click(settings_plugin):
    """测试偏好设置菜单项点击"""
    print("\n=== 测试偏好设置菜单项点击 ===")
    
    if not settings_plugin:
        print("❌ SettingsPlugin不存在，无法测试")
        return False
    
    try:
        # 捕获输出
        output_capture = OutputCapture()
        
        # 重定向stdout
        old_stdout = sys.stdout
        sys.stdout = output_capture
        
        try:
            # 直接调用偏好设置方法
            print("直接调用_open_preferences_dialog方法...")
            settings_plugin._open_preferences_dialog()
            
        finally:
            # 恢复stdout
            sys.stdout = old_stdout
        
        # 获取输出
        captured_output = output_capture.get_output()
        print(f"捕获的输出:\n{captured_output}")
        
        # 检查是否有预期的输出
        if "偏好设置菜单项被点击" in captured_output:
            print("✅ 偏好设置方法被正确调用")
            return True
        else:
            print("❌ 偏好设置方法调用异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试偏好设置菜单项点击失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_action_trigger(menu_bar):
    """测试菜单动作触发"""
    print("\n=== 测试菜单动作触发 ===")
    
    if not menu_bar:
        print("❌ 菜单栏不存在，无法测试")
        return False
    
    try:
        # 查找设置菜单
        settings_menu = None
        for action in menu_bar.actions():
            if "设置" in action.text():
                settings_menu = action.menu()
                break
        
        if not settings_menu:
            print("❌ 未找到设置菜单")
            return False
        
        print("✅ 找到设置菜单")
        
        # 查找偏好设置菜单项
        preferences_action = None
        for action in settings_menu.actions():
            if "偏好设置" in action.text() or "PREFERENCES" in action.text():
                preferences_action = action
                break
        
        if not preferences_action:
            print("❌ 未找到偏好设置菜单项")
            return False
        
        print("✅ 找到偏好设置菜单项")
        
        # 捕获输出
        output_capture = OutputCapture()
        old_stdout = sys.stdout
        sys.stdout = output_capture
        
        try:
            # 触发菜单项
            print("触发偏好设置菜单项...")
            preferences_action.trigger()
            
            # 等待一下让信号处理完成
            QApplication.processEvents()
            
        finally:
            sys.stdout = old_stdout
        
        captured_output = output_capture.get_output()
        print(f"菜单触发后的输出:\n{captured_output}")
        
        if "偏好设置菜单项被点击" in captured_output:
            print("✅ 菜单项触发成功")
            return True
        else:
            print("❌ 菜单项触发失败或无响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试菜单动作触发失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_window():
    """创建测试窗口"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("偏好设置菜单响应测试")
    main_window.resize(800, 600)
    
    # 创建菜单栏
    menu_bar = main_window.menuBar()
    
    # 创建设置插件
    settings_plugin = None
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        settings_plugin = SettingsPlugin(menu_bar)
        settings_plugin.load()
        print("✅ 设置菜单已添加到主窗口")
    except Exception as e:
        print(f"❌ 设置菜单添加失败: {e}")
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
偏好设置菜单响应测试

测试内容:
1. SettingsPlugin的创建和加载
2. 偏好设置菜单项的点击响应
3. 菜单动作的触发机制

测试方法:
1. 点击菜单栏中的"设置" -> "偏好设置"
2. 点击下方的测试按钮进行自动化测试
3. 查看输出日志了解详细信息
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    button_layout = QHBoxLayout()
    
    test_creation_btn = QPushButton("测试插件创建")
    test_click_btn = QPushButton("测试菜单点击")
    test_trigger_btn = QPushButton("测试菜单触发")
    run_all_btn = QPushButton("运行所有测试")
    
    button_layout.addWidget(test_creation_btn)
    button_layout.addWidget(test_click_btn)
    button_layout.addWidget(test_trigger_btn)
    button_layout.addWidget(run_all_btn)
    layout.addLayout(button_layout)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 连接按钮事件
    def test_creation():
        output_text.clear()
        plugin, menu = test_settings_plugin_creation()
        output_text.append("插件创建测试完成")
    
    def test_click():
        if settings_plugin:
            result = test_preferences_menu_click(settings_plugin)
            output_text.append(f"菜单点击测试结果: {'成功' if result else '失败'}")
        else:
            output_text.append("设置插件不存在，无法测试")
    
    def test_trigger():
        result = test_menu_action_trigger(menu_bar)
        output_text.append(f"菜单触发测试结果: {'成功' if result else '失败'}")
    
    def run_all_tests():
        output_text.clear()
        output_text.append("开始运行所有测试...\n")
        
        # 测试1: 插件创建
        plugin, menu = test_settings_plugin_creation()
        output_text.append("✅ 插件创建测试完成\n")
        
        # 测试2: 菜单点击
        if plugin:
            result1 = test_preferences_menu_click(plugin)
            output_text.append(f"✅ 菜单点击测试: {'成功' if result1 else '失败'}\n")
        
        # 测试3: 菜单触发
        result2 = test_menu_action_trigger(menu_bar)
        output_text.append(f"✅ 菜单触发测试: {'成功' if result2 else '失败'}\n")
        
        output_text.append("所有测试完成!")
    
    test_creation_btn.clicked.connect(test_creation)
    test_click_btn.clicked.connect(test_click)
    test_trigger_btn.clicked.connect(test_trigger)
    run_all_btn.clicked.connect(run_all_tests)
    
    # 自动运行初始测试
    QTimer.singleShot(1000, run_all_tests)
    
    main_window.show()
    return app.exec_()

def main():
    print("🔍 偏好设置菜单响应测试...")
    print("=" * 50)
    
    # 运行各项测试
    settings_plugin, menu_bar = test_settings_plugin_creation()
    
    if settings_plugin:
        test_preferences_menu_click(settings_plugin)
    
    if menu_bar:
        test_menu_action_trigger(menu_bar)
    
    print("\n" + "=" * 50)
    print("启动测试窗口...")
    
    return create_test_window()

if __name__ == "__main__":
    sys.exit(main())
