# encoding=utf-8
'''
Created on 2019年11月20日

@author: 10247557
'''
DOCUMENTATION = 'documentation'
SETUP = 'setup'
TEARDOWN = 'teardown'
TIMEOUT = 'timeout'
TEMPLATE = 'template'
TAGS = 'tags'

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParser import ItemParser
from utility.Singleton import Singleton
from model.data_file.TestCases import TestCase


@Singleton
class TestcaseItemParser(ItemParser):

    def query(self, item):
        self._item_list = []
        self._item_list.append(item)
        self._item = item
        if hasattr(self._item._data_file, 'query'):
            self._content = self._item._data_file.query()
            self._get_documentation()
            self._get_setup()
            self._get_teardomn()
            self._get_timeout()
            self._get_template()
            self._get_tags()
            self._get_table()
        else:
            self._content = {}
            self.documentation = None
            self.setup = None
            self.teardown = None
            self.timeout = None
            self.template = None
            self.tags = None
            self.table = []

    def requery(self):
        self.query(self._item)

    def get_cur_data_file(self, item):
        return item.parent()._data_file

    def _get_documentation(self):
        self.documentation = self._content.documentation if hasattr(self._content, DOCUMENTATION) else None

    def _get_setup(self):
        self.setup = self._content.setup if hasattr(self._content, SETUP) else None

    def _get_teardomn(self):
        self.teardown = self._content.teardown if hasattr(self._content, TEARDOWN) else None

    def _get_timeout(self):
        self.timeout = self._content.timeout if hasattr(self._content, TIMEOUT) else None

    def _get_template(self):
        self.template = self._content.template if hasattr(self._content, TEMPLATE) else None

    def _get_tags(self):
        self.tags = self._content.tags if hasattr(self._content, TAGS) else None

    def _get_table(self):
        if isinstance(self._content, TestCase):
            self.table = self._content.body

    def modify(self, area_type, content):
        self._content.modify(area_type, content)
        SignalDistributor().editor_modify(self._item, {'type': 'other', 'operator': 'modify'})
