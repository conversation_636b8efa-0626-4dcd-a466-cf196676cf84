#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据持久性修复
验证同一个用例多次点击后Setup和Teardown内容是否保持一致
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer

def test_line_edit_area_persistence():
    """测试LineEditArea数据持久性"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建四个LineEditArea实例，模拟Setup、Teardown、Arguments、Return Value
        setup_area = LineEditArea(parent, 'Setup')
        setup_area.load()
        
        teardown_area = LineEditArea(parent, 'Teardown')
        teardown_area.load()
        
        arguments_area = LineEditArea(parent, 'Arguments')
        arguments_area.load()
        
        return_value_area = LineEditArea(parent, 'Return Value')
        return_value_area.load()
        
        print("✅ LineEditArea实例创建成功")
        
        # 模拟第一次点击用例，填充数据
        test_data = {
            'setup': 'Open Browser    chrome',
            'teardown': 'Close Browser',
            'arguments': '${url}    ${timeout}',
            'return_value': '${result}'
        }
        
        print("\n=== 第一次填充数据 ===")
        setup_area.fill_data(test_data['setup'])
        print(f"Setup填充: {test_data['setup']}")
        
        teardown_area.fill_data(test_data['teardown'])
        print(f"Teardown填充: {test_data['teardown']}")
        
        arguments_area.fill_data(test_data['arguments'])
        print(f"Arguments填充: {test_data['arguments']}")
        
        return_value_area.fill_data(test_data['return_value'])
        print(f"Return Value填充: {test_data['return_value']}")
        
        # 等待一段时间，让垂直居中完成
        import time
        time.sleep(0.2)
        
        # 获取第一次填充后的数据
        first_setup = setup_area.get_data()
        first_teardown = teardown_area.get_data()
        first_arguments = arguments_area.get_data()
        first_return_value = return_value_area.get_data()
        
        print(f"\n第一次获取的数据:")
        print(f"Setup: {first_setup}")
        print(f"Teardown: {first_teardown}")
        print(f"Arguments: {first_arguments}")
        print(f"Return Value: {first_return_value}")
        
        # 模拟第二次点击同一个用例，再次填充相同数据
        print("\n=== 第二次填充相同数据 ===")
        setup_area.fill_data(test_data['setup'])
        print(f"Setup再次填充: {test_data['setup']}")
        
        teardown_area.fill_data(test_data['teardown'])
        print(f"Teardown再次填充: {test_data['teardown']}")
        
        arguments_area.fill_data(test_data['arguments'])
        print(f"Arguments再次填充: {test_data['arguments']}")
        
        return_value_area.fill_data(test_data['return_value'])
        print(f"Return Value再次填充: {test_data['return_value']}")
        
        # 等待一段时间，让垂直居中完成
        time.sleep(0.2)
        
        # 获取第二次填充后的数据
        second_setup = setup_area.get_data()
        second_teardown = teardown_area.get_data()
        second_arguments = arguments_area.get_data()
        second_return_value = return_value_area.get_data()
        
        print(f"\n第二次获取的数据:")
        print(f"Setup: {second_setup}")
        print(f"Teardown: {second_teardown}")
        print(f"Arguments: {second_arguments}")
        print(f"Return Value: {second_return_value}")
        
        # 比较两次数据是否一致
        print(f"\n=== 数据一致性检查 ===")
        setup_consistent = (first_setup == second_setup)
        teardown_consistent = (first_teardown == second_teardown)
        arguments_consistent = (first_arguments == second_arguments)
        return_value_consistent = (first_return_value == second_return_value)
        
        print(f"Setup数据一致: {'✅' if setup_consistent else '❌'}")
        print(f"Teardown数据一致: {'✅' if teardown_consistent else '❌'}")
        print(f"Arguments数据一致: {'✅' if arguments_consistent else '❌'}")
        print(f"Return Value数据一致: {'✅' if return_value_consistent else '❌'}")
        
        all_consistent = setup_consistent and teardown_consistent and arguments_consistent and return_value_consistent
        
        if all_consistent:
            print("\n✅ 所有数据在多次点击后保持一致，修复成功！")
        else:
            print("\n❌ 部分数据在多次点击后不一致，需要进一步修复")
        
        return {
            'setup_area': setup_area,
            'teardown_area': teardown_area,
            'arguments_area': arguments_area,
            'return_value_area': return_value_area,
            'consistent': all_consistent
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("数据持久性修复测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
数据持久性修复测试

问题: 同一个用例，点击第一次Setup和Teardown里都有内容，再点一次相同的用例，内容为空

原因: textChanged信号在setDocumentMargin时被意外触发，导致_modify_data被调用，清空了数据

修复: 
1. 在fill_data、_center_text_vertically、_do_center_text方法中临时断开textChanged信号
2. 完成操作后重新连接信号
3. 避免在设置文档边距时意外触发数据修改

测试: 点击按钮测试多次填充相同数据是否保持一致
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试数据持久性")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试结果
    test_result = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始数据持久性测试 ===")
        
        nonlocal test_result
        test_result = test_line_edit_area_persistence()
        
        if test_result:
            if test_result['consistent']:
                output_text.append("✅ 数据持久性测试通过")
                output_text.append("多次点击同一用例后，Setup、Teardown、Arguments、Return Value内容保持一致")
            else:
                output_text.append("❌ 数据持久性测试失败")
                output_text.append("多次点击同一用例后，部分内容丢失")
            
            output_text.append("\n=== 测试完成 ===")
            output_text.append("如果测试通过，说明修复成功")
        else:
            output_text.append("❌ 测试执行失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 数据持久性修复测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_line_edit_area_persistence()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
