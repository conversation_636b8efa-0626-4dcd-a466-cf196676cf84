# coding=utf-8
'''
Created on 2019年11月4日

@author: 10247557
'''
import logging
import traceback

from PyQt5.Qt import Qt, QApplication, QCursor
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtWidgets import QHBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtGui import QTextOption, QTextCursor

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.Formator import parse_value
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer, \
    ColorizationSettings
from model.CurrentItem import CurrentItem
from view.common.dialog.SettingsDialog import SettingsDialog
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper


class LineEditArea(object):

    def __init__(self, parent, lable_name):
        self._parent = parent
        self._lable_name = lable_name
        self._text = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._label = QLabel(self._lable_name)
        self._label.setFixedWidth(170)
        self._line = LineEdit()
        self._line.setFixedHeight(25)
        self._line.setReadOnly(True)

        # 设置文本垂直居中对齐
        self._line.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self._line.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self._set_color()
        self._line.clicked.connect(self._show_dialog)
        self._line.jump.connect(self._jump)
        self._line.textChanged.connect(self._modify_data)
        self._clear_btn = QPushButton('Clear', self._parent)
        self._clear_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._clear_btn.setFixedSize(120, 22)
        self._clear_btn.clicked.connect(self._clear_edit_area)
        self._set_layout()

        # 连接主题变化信号
        self._connect_theme_signal()

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._set_color()
        # 主题切换后重新居中文本，因为不同主题的字体大小可能不同
        QTimer.singleShot(100, self._center_text_vertically)

    def _jump(self):
        self._specified_keyword_jumper = SpecifiedKeywordJumper()
        line = self._line.toPlainText()
        keyword_name = line.split('|')[0].strip()
        keyword_path = self._specified_keyword_jumper.get_keyword_path_from_local_repository(keyword_name)
        self._specified_keyword_jumper.get_keyword_item(keyword_path, keyword_name)

    def _center_text_vertically(self):
        """确保文本垂直居中显示"""
        try:
            # 临时断开textChanged信号，避免在设置边距时触发_modify_data
            try:
                self._line.textChanged.disconnect(self._modify_data)
                signal_disconnected = True
            except:
                signal_disconnected = False

            # 先重置文档边距，确保计算准确
            self._line.document().setDocumentMargin(0)

            # 强制重绘文档，使用正确的方法
            self._line.update()

            # 重新连接信号
            if signal_disconnected:
                self._line.textChanged.connect(self._modify_data)

            # 等待布局完成后再计算
            QTimer.singleShot(10, self._do_center_text)
        except Exception as e:
            print(f"文本垂直居中失败: {e}")

    def _do_center_text(self):
        """执行实际的文本居中操作"""
        try:
            # 临时断开textChanged信号，避免在设置边距时触发_modify_data
            try:
                self._line.textChanged.disconnect(self._modify_data)
                signal_disconnected = True
            except:
                signal_disconnected = False

            # 获取文档和视口高度
            doc_height = self._line.document().size().height()
            viewport_height = self._line.viewport().height()

            # 只有当视口高度大于文档高度时才进行居中
            if viewport_height > doc_height and doc_height > 0:
                # 计算需要的上边距来实现垂直居中
                margin = max(0, (viewport_height - doc_height) / 2)
                self._line.document().setDocumentMargin(margin)

            # 移动光标到文档开始位置，避免选中文本
            cursor = self._line.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.clearSelection()
            self._line.setTextCursor(cursor)

            # 重新连接信号
            if signal_disconnected:
                self._line.textChanged.connect(self._modify_data)
        except Exception as e:
            print(f"执行文本居中失败: {e}")

    def _set_color(self):
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()

        if self._line.toPlainText():
            # 有内容时的背景色
            if current_theme == 'dark':
                # 深色主题：使用更深的背景色，与主题一致但略微浅一些
                self._line.setStyleSheet("background-color: #353535; color: #E0E0E0;")
            elif current_theme == 'dark_orange':
                # 深色橙色主题：使用与主题一致的深色背景
                self._line.setStyleSheet("background-color: #3A3A3A; color: #b1b1b1;")
            else:
                self._line.setStyleSheet("background-color: #ffffff; color: #000000;")
        else:
            # 无内容时的背景色
            if current_theme == 'dark':
                # 深色主题：使用更深的背景色
                self._line.setStyleSheet("background-color: #2A2A2A; color: #A0A0A0;")
            elif current_theme == 'dark_orange':
                # 深色橙色主题：使用更深的背景色
                self._line.setStyleSheet("background-color: #2D2D2D; color: #808080;")
            else:
                self._line.setStyleSheet("background-color: #c0c0c0; color: #000000;")

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._label, 1)
        self._layout.addWidget(self._line, 6)
        self._layout.addWidget(self._clear_btn, 1)

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._line.setVisible(bool_value)
        self._clear_btn.setVisible(bool_value)

    def _clear_edit_area(self):
        self._line.clear()
        self._set_color()

    def fill_data(self, text, force_update=False):
        try:
            # 检查是否需要更新数据，避免覆盖用户的修改
            # 但如果是强制更新（比如从对话框调用），则跳过检查
            if not force_update and text == self._text:
                # 数据没有变化，不需要重新填充
                return

            self._text = text
            if text is None:
                text = ''

            # 临时断开textChanged信号，避免在设置文本时触发_modify_data
            self._line.textChanged.disconnect(self._modify_data)

            # 先重置文档状态，确保一致性
            self._line.document().setDocumentMargin(0)

            if isinstance(text, list):
                keyword = text[0].split(' | ')[0]
                if text[0] and text[1]:
                    text = ' | ' .join(text)
                else:
                    text = '' .join(text)
            else:
                keyword = parse_value(text)[0].split(' | ')[0]
            if Colorizer()._is_keyword(keyword):
                color = ColorizationSettings().get_keyword_color()
                color = (int(color[0]), int(color[1]), int(color[2]))
                self._line.setText('<a href = %s"><font style="color:rgb%s;">%s</font></a>%s' % (keyword, color, keyword, text.replace(keyword, '')))
            else:
                self._line.setFontUnderline(False)
                self._line.setTextColor(Qt.black)
                self._line.setPlainText(text)

            # 设置颜色
            self._set_color()

            # 重新连接textChanged信号
            self._line.textChanged.connect(self._modify_data)

            # 延迟执行文本垂直居中，确保文本设置完成后再居中
            QTimer.singleShot(50, self._center_text_vertically)
        except Exception:
            logging.error('============teardown or timeout or arguments or return value except=========')
            traceback.print_exc()

    def get_data(self):
        text = self._line.toPlainText().rstrip('\n')
        return parse_value(text) if text else None

    def _modify_data(self):
        result = self.get_data()
        if self._text != result:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify(self._lable_name.replace(' ', '_').lower(), result)
            # 更新内部文本状态，确保下次fill_data时不会覆盖用户的修改
            self._text = result

    def _show_dialog(self):
        self.dialog = SettingsDialog(self, self._lable_name)
        self.dialog.show()
        self.dialog.set_text(parse_value(self._line.toPlainText()))


class LineEdit(QTextEdit):
    clicked = pyqtSignal()
    jump = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        # 设置单行模式的属性
        self.setLineWrapMode(QTextEdit.NoWrap)
        self.setWordWrapMode(QTextOption.NoWrap)

    def resizeEvent(self, event):
        """窗口大小改变时重新居中文本"""
        super().resizeEvent(event)
        # 延迟执行居中，确保布局完成
        QTimer.singleShot(100, self._center_text_if_needed)

    def _center_text_if_needed(self):
        """如果需要的话，重新居中文本"""
        try:
            # 先重置边距
            self.document().setDocumentMargin(0)

            # 强制重绘，使用正确的方法
            self.update()

            # 再次延迟计算，确保布局完成
            QTimer.singleShot(10, self._do_actual_center)
        except Exception:
            pass

    def _do_actual_center(self):
        """执行实际的居中操作"""
        try:
            doc_height = self.document().size().height()
            viewport_height = self.viewport().height()

            # 只有当视口高度大于文档高度且文档有内容时才居中
            if viewport_height > doc_height and doc_height > 0:
                margin = max(0, (viewport_height - doc_height) / 2)
                self.document().setDocumentMargin(margin)
        except Exception:
            pass

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if QApplication.keyboardModifiers() == Qt.ControlModifier:
                self.jump.emit()
            else:
                self.clicked.emit()


if __name__ == "__main__":
    str = 'sdfa| fgsg '
    result = str.rsplit('| #', 1)
    print(result)
    print(result[1].replace('\\|', '|'))
