#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试偏好设置窗口置顶和深色橙色主题下表格文字染色
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout, QMenuBar, QTextEdit)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor

def create_test_table_with_robot_content():
    """创建包含RobotFramework内容的测试表格"""
    table = QTableWidget(15, 6)
    table.setHorizontalHeaderLabels(['关键字', '参数1', '参数2', '参数3', '参数4', '注释'])
    
    # 添加RobotFramework测试数据
    test_data = [
        ['*** Test Cases ***', '', '', '', '', ''],
        ['测试用例1', '', '', '', '', ''],
        ['    Log', 'Hello World', '', '', '', '# 输出日志'],
        ['    ${variable}', '=', 'Set Variable', 'test_value', '', ''],
        ['    FOR', '${item}', 'IN', '@{list}', '', ''],
        ['        Log', '${item}', '', '', '', ''],
        ['    END', '', '', '', '', ''],
        ['    Sleep', '1s', '', '', '', '# 等待1秒'],
        ['    Should Be Equal', '${variable}', 'test_value', '', '', ''],
        ['    ${result}', '=', 'Get Text', 'xpath=//div', '', ''],
        ['    Comment', '这是一个注释行', '', '', '', ''],
        ['    \\', '继续上一行', '', '', '', ''],
        ['*** Keywords ***', '', '', '', '', ''],
        ['自定义关键字', '', '', '', '', ''],
        ['    [Arguments]', '${arg1}', '${arg2}', '', '', '']
    ]
    
    for row, row_data in enumerate(test_data):
        for col, cell_data in enumerate(row_data):
            item = QTableWidgetItem(cell_data)
            table.setItem(row, col, item)
    
    return table

def apply_colorization_manually(table):
    """手动应用文字染色（模拟Colorizer的效果）"""
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
        
        # 获取当前主题的颜色设置
        colorization_settings = ColorizationSettings()
        colorization_settings.refresh_theme_colors()
        
        # 获取颜色
        keyword_color = colorization_settings.get_keyword_color()
        variable_color = colorization_settings.get_variable_color()
        text_color = colorization_settings.get_text_color()
        comment_color = colorization_settings.get_comment_color()
        background_color = colorization_settings.get_background_color()
        
        print(f"当前主题颜色设置:")
        print(f"  关键字颜色: {keyword_color}")
        print(f"  变量颜色: {variable_color}")
        print(f"  文字颜色: {text_color}")
        print(f"  注释颜色: {comment_color}")
        print(f"  背景颜色: {background_color}")
        
        # 转换为QColor
        keyword_qcolor = QColor(keyword_color[0], keyword_color[1], keyword_color[2])
        variable_qcolor = QColor(variable_color[0], variable_color[1], variable_color[2])
        text_qcolor = QColor(text_color[0], text_color[1], text_color[2])
        comment_qcolor = QColor(comment_color[0], comment_color[1], comment_color[2])
        background_qcolor = QColor(background_color[0], background_color[1], background_color[2])
        
        # 应用颜色到表格单元格
        for row in range(table.rowCount()):
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    text = item.text()
                    
                    # 设置背景色
                    item.setBackground(background_qcolor)
                    
                    # 根据内容设置前景色
                    if text.startswith('#'):
                        # 注释
                        item.setForeground(comment_qcolor)
                    elif text.startswith('${') and text.endswith('}'):
                        # 变量
                        item.setForeground(variable_qcolor)
                    elif text in ['Log', 'Sleep', 'Should Be Equal', 'Get Text', 'Set Variable', 
                                  'FOR', 'END', 'Comment', 'Arguments']:
                        # 关键字
                        item.setForeground(keyword_qcolor)
                    elif text == '\\':
                        # 续行符
                        item.setForeground(comment_qcolor)
                        item.setBackground(comment_qcolor)
                    elif text.startswith('***') and text.endswith('***'):
                        # 节标题
                        item.setForeground(keyword_qcolor)
                    else:
                        # 普通文字
                        item.setForeground(text_qcolor)
        
        print("表格文字染色已应用")
        
    except Exception as e:
        print(f"应用文字染色失败: {e}")
        import traceback
        traceback.print_exc()

def test_preferences_dialog():
    """测试偏好设置对话框"""
    print("=== 测试偏好设置对话框 ===")
    
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        from PyQt5.QtWidgets import QMenuBar
        
        # 创建测试菜单栏
        menu_bar = QMenuBar()
        
        # 创建设置插件
        settings_plugin = SettingsPlugin(menu_bar)
        settings_plugin.load()
        
        print("设置插件加载成功")
        
        # 测试偏好设置对话框
        settings_plugin._open_preferences_dialog()
        print("偏好设置对话框测试完成")
        
        return True
        
    except Exception as e:
        print(f"偏好设置对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_colorization_settings():
    """测试ColorizationSettings"""
    print("\n=== 测试ColorizationSettings ===")
    
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
        
        colorization = ColorizationSettings()
        
        print(f"关键字颜色: {colorization.get_keyword_color()}")
        print(f"变量颜色: {colorization.get_variable_color()}")
        print(f"文字颜色: {colorization.get_text_color()}")
        print(f"背景颜色: {colorization.get_background_color()}")
        print(f"注释颜色: {colorization.get_comment_color()}")
        
        # 测试主题切换
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            
            print(f"当前主题: {current_theme}")
            
            # 切换主题并测试颜色变化
            new_theme = 'dark_orange' if current_theme != 'dark_orange' else 'light'
            if theme_manager.set_theme(new_theme):
                print(f"主题已切换到: {new_theme}")
                print(f"切换后关键字颜色: {colorization.get_keyword_color()}")
                print(f"切换后变量颜色: {colorization.get_variable_color()}")
                
                # 切换回原主题
                theme_manager.set_theme(current_theme)
                print(f"已切换回原主题: {current_theme}")
            
        except Exception as e:
            print(f"主题切换测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"ColorizationSettings测试失败: {e}")
        return False

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("偏好设置置顶和表格文字染色测试")
    main_window.resize(1200, 800)
    
    # 创建菜单栏
    menu_bar = main_window.menuBar()
    
    # 添加设置菜单
    try:
        from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin
        settings_plugin = SettingsPlugin(menu_bar)
        settings_plugin.load()
        print("✅ 设置菜单已添加")
    except Exception as e:
        print(f"❌ 设置菜单添加失败: {e}")
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
偏好设置置顶和表格文字染色测试

修复内容:
1. 偏好设置窗口置顶显示
2. 深色橙色主题下编辑页签表格文字染色

测试方法:
1. 点击菜单栏"设置" -> "偏好设置"，确认对话框置顶显示
2. 点击"应用文字染色"按钮查看表格中的文字颜色
3. 点击"切换主题"按钮测试主题切换时的颜色变化
4. 观察关键字、变量、注释等是否有不同颜色
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    colorize_btn = QPushButton("应用文字染色")
    theme_btn = QPushButton("切换主题")
    test_prefs_btn = QPushButton("测试偏好设置")
    test_colorization_btn = QPushButton("测试颜色设置")
    
    button_layout.addWidget(colorize_btn)
    button_layout.addWidget(theme_btn)
    button_layout.addWidget(test_prefs_btn)
    button_layout.addWidget(test_colorization_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_test_table_with_robot_content()
    layout.addWidget(QLabel("RobotFramework测试表格 - 观察文字颜色:"))
    layout.addWidget(table)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    output_text.setMaximumHeight(150)
    layout.addWidget(QLabel("测试输出:"))
    layout.addWidget(output_text)
    
    # 连接按钮事件
    def apply_colorization():
        apply_colorization_manually(table)
        output_text.append("文字染色已应用")
    
    def switch_theme():
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current = theme_manager.get_current_theme()
            new_theme = 'dark_orange' if current != 'dark_orange' else 'light'
            if theme_manager.set_theme(new_theme):
                output_text.append(f"主题已切换: {current} -> {new_theme}")
                # 重新应用染色
                apply_colorization_manually(table)
            else:
                output_text.append("主题切换失败")
        except Exception as e:
            output_text.append(f"主题切换失败: {e}")
    
    def test_prefs():
        result = test_preferences_dialog()
        output_text.append(f"偏好设置测试: {'成功' if result else '失败'}")
    
    def test_colorization():
        result = test_colorization_settings()
        output_text.append(f"颜色设置测试: {'成功' if result else '失败'}")
    
    colorize_btn.clicked.connect(apply_colorization)
    theme_btn.clicked.connect(switch_theme)
    test_prefs_btn.clicked.connect(test_prefs)
    test_colorization_btn.clicked.connect(test_colorization)
    
    # 初始应用染色
    apply_colorization()
    
    # 自动运行测试
    def run_auto_tests():
        output_text.append("=== 自动测试开始 ===")
        test_colorization_settings()
        output_text.append("=== 自动测试完成 ===")
    
    QTimer.singleShot(1000, run_auto_tests)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
