# coding=utf-8
'''
Created on 2019年10月27日

@author: 10247557
'''
from PyQt5.QtWidgets import QVBoxLayout

from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.component.Tag import Tag
from controller.system_plugin.edit.view.DefaultEdit import DefaultEdit
from model.CurrentItem import CurrentItem


class TestcaseItemEdit(DefaultEdit):

    def __init__(self, parent):
        super().__init__(parent)
        self._layout = None
#         self._is_visible = True

    def load(self):
        if self._layout:
            return self._layout
        super().load()
        self._setup = self._set_setup_area()
        self._template = self._set_template_area()
        self._tag = self._set_tag_area()
        self._set_layout()
        return self._layout

    def _set_layout(self):
        self._layout = QVBoxLayout()
        self._layout.addLayout(self._path)
        self._layout.addLayout(self._settings)
#         self._settings_btn.clicked.connect(self._set_visible)
        self._layout.addLayout(self._documentation.get_layout())
        self._layout.addLayout(self._setup.get_layout())
        self._layout.addLayout(self._teardown.get_layout())
        self._layout.addLayout(self._timeout.get_layout())
        self._layout.addLayout(self._template.get_layout())
        self._layout.addLayout(self._tag.get_layout())
        self._layout.addLayout(self._table.get_layout(), 5)
        self._set_stretch_factor()

    def _set_stretch_factor(self):
        self._layout.setStretchFactor(self._table.get_layout(), 5)

    def _set_visible_area(self):
        super()._set_visible_area()
        self._setup.set_visible(self._is_visible)
        self._template.set_visible(self._is_visible)
        self._tag.set_visible(self._is_visible)

    def _set_setup_area(self):
        setup = LineEditArea(self._parent_window, 'Setup')
        setup.load()
        return setup

    def _set_template_area(self):
        template = LineEditArea(self._parent_window, 'Template')
        template.load()
        return template

    def _set_tag_area(self):
        tag = Tag(self._parent_window, 'Tags')
        tag.load()
        return tag

    def fill_data(self, parsed_item):
        super().fill_data(parsed_item)
        self._setup.fill_data(parsed_item.setup)
        self._template.fill_data(parsed_item.template)
        self._tag.fill_data(parsed_item.tags)
        
