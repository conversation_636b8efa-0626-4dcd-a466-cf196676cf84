# coding=utf-8
'''
Created on 2024年1月1日

@author: AI Assistant
'''

import os
import codecs
from PyQt5.QtCore import QObject, pyqtSignal

from settings.UserSettings import UserSettings
from utility.PluginRepository import PluginRepository
from utility.Singleton import Singleton


# 主题配置
THEMES = {
    'light': {
        'name': '浅色主题',
        'file': 'default.qss',
        'description': '经典的浅色界面主题'
    },
    'dark': {
        'name': '深色主题',
        'file': 'dark_theme.qss',
        'description': '护眼的深色界面主题'
    },
    'dark_orange': {
        'name': '深色橙色主题',
        'file': 'dark_orange.qss',
        'description': '带橙色强调的深色主题'
    }
}

THEME_SETTING_KEY = 'UI_THEME'
SYSTEM_ROOT_DIR = "{}/../../..".format(os.path.abspath(os.path.dirname(__file__)))


@Singleton
class ThemeManager(QObject):
    """主题管理器"""

    # 主题改变信号
    theme_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self._current_theme = 'light'
        self._load_current_theme()

    def get_themes(self):
        """获取所有可用主题"""
        return THEMES

    def get_current_theme(self):
        """获取当前主题"""
        return self._current_theme

    def set_theme(self, theme_id, save_preference=True):
        """设置主题"""
        if theme_id not in THEMES:
            print(f"未知的主题ID: {theme_id}")
            return False

        try:
            # 应用主题样式
            if self._apply_theme_style(theme_id):
                self._current_theme = theme_id

                # 保存主题偏好
                if save_preference:
                    self._save_theme_preference(theme_id)

                # 通知文本编辑器应用主题
                self.notify_text_editors(theme_id)

                # 通知表格颜色设置刷新
                self.notify_colorization_settings()

                # 发送主题改变信号
                self.theme_changed.emit(theme_id)

                print(f"主题已切换到: {THEMES[theme_id]['name']}")
                return True
            else:
                print(f"应用主题失败: {theme_id}")
                return False

        except Exception as e:
            print(f"设置主题时出错: {e}")
            return False

    def _apply_theme_style(self, theme_id):
        """应用主题样式"""
        try:
            theme_info = THEMES[theme_id]
            style_file = os.path.join(SYSTEM_ROOT_DIR, 'resources', 'qss', theme_info['file'])

            if not os.path.exists(style_file):
                print(f"主题文件不存在: {style_file}")
                return False

            # 获取主窗口
            main_frame = PluginRepository().find("MAIN_FRAME")
            if not main_frame:
                print("未找到主窗口")
                return False

            # 读取并应用样式
            with codecs.open(style_file, "r", encoding='utf-8') as f:
                style = f.read()

            main_frame.setStyleSheet(style)
            return True

        except Exception as e:
            print(f"应用主题样式时出错: {e}")
            return False

    def _save_theme_preference(self, theme_id):
        """保存主题偏好设置"""
        try:
            user_settings = UserSettings()
            user_settings.set_value(THEME_SETTING_KEY, theme_id)
        except Exception as e:
            print(f"保存主题设置时出错: {e}")

    def _load_current_theme(self):
        """加载当前主题设置"""
        try:
            user_settings = UserSettings()
            saved_theme = user_settings.get_value(THEME_SETTING_KEY, False)

            # 如果没有保存的主题，使用默认的浅色主题
            if not saved_theme or saved_theme not in THEMES:
                saved_theme = 'light'

            self._current_theme = saved_theme

        except Exception as e:
            print(f"加载主题设置时出错: {e}")
            self._current_theme = 'light'

    def apply_saved_theme(self):
        """应用保存的主题（用于应用启动时）"""
        self.set_theme(self._current_theme, save_preference=False)

    def get_theme_info(self, theme_id):
        """获取主题信息"""
        return THEMES.get(theme_id, None)

    def is_dark_theme(self, theme_id=None):
        """判断是否为深色主题"""
        if theme_id is None:
            theme_id = self._current_theme
        return theme_id in ['dark', 'dark_orange']

    def get_theme_list(self):
        """获取主题列表（用于UI显示）"""
        return [(theme_id, theme_info['name'], theme_info['description'])
                for theme_id, theme_info in THEMES.items()]

    def reset_to_default(self):
        """重置为默认主题"""
        self.set_theme('light')

    def refresh_current_theme(self):
        """刷新当前主题（重新应用样式）"""
        self._apply_theme_style(self._current_theme)

    def notify_text_editors(self, theme_id):
        """通知所有文本编辑器应用主题"""
        try:
            from utility.PluginRepository import PluginRepository

            # 1. 查找主要的文本编辑器
            text_editor = PluginRepository().find('TEXT_EDIT')
            if text_editor and hasattr(text_editor, '_apply_theme_from_manager'):
                text_editor._apply_theme_from_manager(theme_id)
                print(f"已通知主文本编辑器应用主题: {theme_id}")

            # 2. 查找编辑插件控制器中的编辑器
            edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
            if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
                editor = edit_plugin_controller._editor
                if editor and hasattr(editor, '_apply_theme_from_manager'):
                    editor._apply_theme_from_manager(theme_id)
                    print(f"已通知编辑插件控制器中的编辑器应用主题: {theme_id}")
                elif editor and hasattr(editor, '_set_theme'):
                    editor._set_theme('dark' if self.is_dark_theme(theme_id) else 'white')
                    print(f"已通知编辑插件控制器中的编辑器应用主题: {theme_id}")

            # 3. 查找其他可能的文本编辑器组件
            # 可以根据需要添加更多编辑器的查找逻辑

        except Exception as e:
            print(f"通知文本编辑器应用主题时出错: {e}")

    def notify_colorization_settings(self):
        """通知表格颜色设置刷新"""
        try:
            from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
            colorization_settings = ColorizationSettings()
            colorization_settings.refresh_theme_colors()
            print("已通知表格颜色设置刷新")
        except Exception as e:
            print(f"通知表格颜色设置刷新时出错: {e}")
