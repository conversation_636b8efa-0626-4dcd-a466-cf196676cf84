#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试状态栏和Tags文本框的深色主题修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QStatusBar, QHBoxLayout)
from PyQt5.QtCore import Qt

def create_test_tags_table():
    """创建测试Tags表格"""
    from controller.system_plugin.edit.view.component.Tag import Tag
    
    # 创建一个简单的父窗口
    parent = QWidget()
    
    # 创建Tag组件
    tag = Tag(parent, "Tags")
    tag.load()
    
    # 填充一些测试数据
    test_tags = ["smoke", "regression", "api"]
    tag.fill_data([" | ".join(test_tags)])
    
    return tag

def apply_dark_theme_styles(widget):
    """应用深色主题样式"""
    dark_style = """
        QMainWindow { 
            background-color: #2B2B2B; 
            color: #E0E0E0; 
        }
        QWidget { 
            background-color: #2B2B2B; 
            color: #E0E0E0; 
        }
        QPushButton { 
            background-color: #404040; 
            color: #E0E0E0; 
            border: 1px solid #555555; 
            padding: 6px 12px;
            border-radius: 4px;
        }
        QPushButton:hover { 
            background-color: #4A90E2; 
            border-color: #357ABD;
        }
        QLabel { 
            color: #E0E0E0; 
        }
        QStatusBar {
            background-color: #3C3C3C;
            color: #E0E0E0;
            border-top: 1px solid #555555;
        }
        QStatusBar QLabel {
            background-color: transparent;
            color: #E0E0E0;
            padding: 2px 4px;
        }
        QStatusBar::item {
            border: none;
            color: #E0E0E0;
        }
        QStatusBar * {
            color: #E0E0E0;
        }
        QTableWidget {
            border: none;
            background-color: #404040;
            color: #E0E0E0;
            gridline-color: #555555;
        }
        QTableWidget::item {
            background-color: #404040;
            color: #E0E0E0;
            border: 1px solid #555555;
            padding: 2px;
        }
        QTableWidget::item:selected {
            background-color: #4A90E2;
            color: #FFFFFF;
        }
    """
    widget.setStyleSheet(dark_style)

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("状态栏和Tags文本框深色主题测试")
    main_window.resize(800, 400)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试状态栏文字颜色和Tags表格的深色主题样式")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建主题切换按钮
    theme_btn = QPushButton("应用深色主题")
    layout.addWidget(theme_btn)
    
    # 创建Tags测试区域
    tags_label = QLabel("Tags表格测试:")
    layout.addWidget(tags_label)
    
    try:
        # 创建Tags组件
        tag = create_test_tags_table()
        layout.addLayout(tag.get_layout())
    except Exception as e:
        error_label = QLabel(f"创建Tags组件失败: {e}")
        layout.addWidget(error_label)
    
    # 创建状态栏
    status_bar = QStatusBar()
    main_window.setStatusBar(status_bar)
    status_bar.showMessage("这是状态栏消息 - 在深色主题下应该可以清楚看到", 0)
    
    # 添加状态栏测试按钮
    status_test_btn = QPushButton("更新状态栏消息")
    layout.addWidget(status_test_btn)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("应用深色主题")
                main_window.setStyleSheet("")
                is_dark = False
                status_bar.showMessage("已切换到浅色主题", 5000)
                print("已切换到浅色主题")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("应用浅色主题")
                apply_dark_theme_styles(main_window)
                is_dark = True
                status_bar.showMessage("已切换到深色主题 - 文字应该清晰可见", 5000)
                print("已切换到深色主题")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                main_window.setStyleSheet("")
                theme_btn.setText("应用深色主题")
                is_dark = False
                status_bar.showMessage("手动切换到浅色主题", 5000)
                print("手动切换到浅色主题")
            else:
                apply_dark_theme_styles(main_window)
                theme_btn.setText("应用浅色主题")
                is_dark = True
                status_bar.showMessage("手动切换到深色主题 - 状态栏文字应该可见", 5000)
                print("手动切换到深色主题")
    
    def update_status():
        import time
        current_time = time.strftime("%H:%M:%S")
        status_bar.showMessage(f"状态栏更新时间: {current_time} - 测试文字可见性", 10000)
    
    theme_btn.clicked.connect(toggle_theme)
    status_test_btn.clicked.connect(update_status)
    
    print("测试窗口已创建")
    print("请点击按钮切换主题，观察:")
    print("1. 状态栏文字在深色主题下是否清晰可见")
    print("2. Tags表格在深色主题下的背景色和文字颜色")
    print("3. 状态栏消息更新时文字是否正常显示")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
