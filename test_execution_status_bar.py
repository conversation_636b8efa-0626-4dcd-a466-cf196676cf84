#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试执行页签状态栏主题修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QLineEdit, QLabel, QHBoxLayout)
from PyQt5.QtCore import Qt

def create_mock_progress_editor():
    """创建模拟的progress_editor"""
    progress_editor = QLineEdit()
    progress_editor.setReadOnly(True)
    progress_editor.setFixedHeight(30)
    progress_editor.setText("执行状态栏 - 测试文字可见性")
    return progress_editor

def apply_light_theme_styles(progress_editor):
    """应用浅色主题样式"""
    progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")

def apply_dark_theme_styles(progress_editor):
    """应用深色主题样式"""
    progress_editor.setStyleSheet("background-color: #404040; color: #E0E0E0; border-width:0; border-style:outset")

def apply_success_styles(progress_editor, is_dark):
    """应用成功状态样式"""
    if is_dark:
        progress_editor.setStyleSheet("background-color: #2E7D32; color: #E8F5E8; border-width:0; border-style:outset")
    else:
        progress_editor.setStyleSheet("background:rgb(155,213,139)")

def apply_failure_styles(progress_editor, is_dark):
    """应用失败状态样式"""
    if is_dark:
        progress_editor.setStyleSheet("background-color: #C62828; color: #FFEBEE; border-width:0; border-style:outset")
    else:
        progress_editor.setStyleSheet("background:rgb(242,125,124)")

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("执行页签状态栏主题测试")
    main_window.resize(800, 400)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试执行页签状态栏在不同主题和状态下的显示效果")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建progress_editor
    progress_editor = create_mock_progress_editor()
    layout.addWidget(QLabel("执行状态栏:"))
    layout.addWidget(progress_editor)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    
    theme_btn = QPushButton("切换到深色主题")
    status_success_btn = QPushButton("模拟执行成功")
    status_failure_btn = QPushButton("模拟执行失败")
    status_clear_btn = QPushButton("清除状态")
    
    button_layout.addWidget(theme_btn)
    button_layout.addWidget(status_success_btn)
    button_layout.addWidget(status_failure_btn)
    button_layout.addWidget(status_clear_btn)
    
    layout.addLayout(button_layout)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("切换到深色主题")
                apply_light_theme_styles(progress_editor)
                main_window.setStyleSheet("")
                is_dark = False
                progress_editor.setText("浅色主题 - 执行状态栏")
                print("已切换到浅色主题")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("切换到浅色主题")
                apply_dark_theme_styles(progress_editor)
                # 应用主窗口深色样式
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                        border-radius: 4px;
                    }
                    QPushButton:hover { 
                        background-color: #4A90E2; 
                        border-color: #357ABD;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                is_dark = True
                progress_editor.setText("深色主题 - 执行状态栏 - 文字应该清晰可见")
                print("已切换到深色主题")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                apply_light_theme_styles(progress_editor)
                main_window.setStyleSheet("")
                theme_btn.setText("切换到深色主题")
                is_dark = False
                progress_editor.setText("手动切换到浅色主题")
                print("手动切换到浅色主题")
            else:
                apply_dark_theme_styles(progress_editor)
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                theme_btn.setText("切换到浅色主题")
                is_dark = True
                progress_editor.setText("手动切换到深色主题 - 文字应该清晰可见")
                print("手动切换到深色主题")
    
    def simulate_success():
        """模拟执行成功状态"""
        apply_success_styles(progress_editor, is_dark)
        if is_dark:
            progress_editor.setText("深色主题 - 执行成功 - 绿色背景浅色文字")
        else:
            progress_editor.setText("浅色主题 - 执行成功 - 绿色背景")
        print("模拟执行成功状态")
    
    def simulate_failure():
        """模拟执行失败状态"""
        apply_failure_styles(progress_editor, is_dark)
        if is_dark:
            progress_editor.setText("深色主题 - 执行失败 - 红色背景浅色文字")
        else:
            progress_editor.setText("浅色主题 - 执行失败 - 红色背景")
        print("模拟执行失败状态")
    
    def clear_status():
        """清除状态"""
        if is_dark:
            apply_dark_theme_styles(progress_editor)
            progress_editor.setText("深色主题 - 默认状态")
        else:
            apply_light_theme_styles(progress_editor)
            progress_editor.setText("浅色主题 - 默认状态")
        print("清除执行状态")
    
    # 连接信号
    theme_btn.clicked.connect(toggle_theme)
    status_success_btn.clicked.connect(simulate_success)
    status_failure_btn.clicked.connect(simulate_failure)
    status_clear_btn.clicked.connect(clear_status)
    
    print("测试窗口已创建")
    print("请点击按钮测试:")
    print("1. 切换主题观察状态栏背景色和文字颜色")
    print("2. 模拟执行成功/失败状态观察颜色变化")
    print("3. 深色主题下状态栏应该是深色背景浅色文字")
    print("4. 失败状态应该是红色背景")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
