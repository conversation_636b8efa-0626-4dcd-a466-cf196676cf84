#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证深色主题修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_css_file():
    """检查CSS文件中的corner button样式"""
    css_file = "resources/qss/dark_theme.qss"

    if not os.path.exists(css_file):
        print(f"❌ CSS文件不存在: {css_file}")
        return False

    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查corner button样式
    corner_styles = [
        "QTableCornerButton::section",
        "QAbstractScrollArea::corner",
        "background-color: #3C3C3C"
    ]

    missing_styles = []
    for style in corner_styles:
        if style not in content:
            missing_styles.append(style)

    if missing_styles:
        print(f"❌ CSS文件中缺少以下样式: {missing_styles}")
        return False
    else:
        print("✅ CSS文件中包含了corner button的深色主题样式")
        return True

def check_table_py():
    """检查Table.py中的修改"""
    table_file = "controller/system_plugin/edit/view/component/table/Table.py"

    if not os.path.exists(table_file):
        print(f"❌ Table.py文件不存在: {table_file}")
        return False

    with open(table_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查关键修改
    required_changes = [
        "from controller.system_plugin.style.ThemeManager import ThemeManager",
        "theme_manager.is_dark_theme()",
        "corner_style",
        "_connect_theme_signal",
        "_on_theme_changed"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ Table.py中缺少以下修改: {missing_changes}")
        return False
    else:
        print("✅ Table.py中包含了主题切换和corner button样式设置")
        return True

def check_line_edit_area_py():
    """检查LineEditArea.py中的修改"""
    line_edit_file = "controller/system_plugin/edit/view/component/LineEditArea.py"

    if not os.path.exists(line_edit_file):
        print(f"❌ LineEditArea.py文件不存在: {line_edit_file}")
        return False

    with open(line_edit_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查关键修改
    required_changes = [
        "from controller.system_plugin.style.ThemeManager import ThemeManager",
        "theme_manager.is_dark_theme()",
        "background-color: #404040",
        "background-color: #333333",
        "_connect_theme_signal",
        "_on_theme_changed"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ LineEditArea.py中缺少以下修改: {missing_changes}")
        return False
    else:
        print("✅ LineEditArea.py中包含了深色主题输入框样式设置")
        return True

def check_status_bar_fixes():
    """检查状态栏修复"""
    css_file = "resources/qss/dark_theme.qss"

    if not os.path.exists(css_file):
        print(f"❌ CSS文件不存在: {css_file}")
        return False

    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查状态栏样式
    status_bar_styles = [
        "QStatusBar QLabel",
        "QStatusBar::item",
        "QStatusBar *",
        "color: #E0E0E0"
    ]

    missing_styles = []
    for style in status_bar_styles:
        if style not in content:
            missing_styles.append(style)

    if missing_styles:
        print(f"❌ 状态栏样式修复不完整，缺少: {missing_styles}")
        return False
    else:
        print("✅ 状态栏深色主题样式修复完成")
        return True

def check_tags_fixes():
    """检查Tags组件修复"""
    tag_file = "controller/system_plugin/edit/view/component/Tag.py"

    if not os.path.exists(tag_file):
        print(f"❌ Tag.py文件不存在: {tag_file}")
        return False

    with open(tag_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查Tags组件修改
    required_changes = [
        "_set_table_style",
        "_connect_theme_signal",
        "_on_theme_changed",
        "background-color: #404040",
        "ThemeManager"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ Tags组件修复不完整，缺少: {missing_changes}")
        return False
    else:
        print("✅ Tags组件深色主题样式修复完成")
        return True

def check_light_theme_corner_button():
    """检查浅色主题corner button修复"""
    css_file = "resources/qss/default.qss"

    if not os.path.exists(css_file):
        print(f"❌ 浅色主题CSS文件不存在: {css_file}")
        return False

    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查浅色主题corner button样式
    required_styles = [
        "QTableCornerButton::section",
        "background-color: #F5F5F5",
        "QAbstractScrollArea::corner"
    ]

    missing_styles = []
    for style in required_styles:
        if style not in content:
            missing_styles.append(style)

    if missing_styles:
        print(f"❌ 浅色主题corner button样式不完整，缺少: {missing_styles}")
        return False
    else:
        print("✅ 浅色主题corner button样式修复完成")
        return True

def check_table_edit_area_fixes():
    """检查表格编辑区域修复"""
    edit_area_file = "controller/system_plugin/edit/view/component/table/TableEditArea.py"

    if not os.path.exists(edit_area_file):
        print(f"❌ TableEditArea.py文件不存在: {edit_area_file}")
        return False

    with open(edit_area_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查表格编辑区域修改
    required_changes = [
        "_set_edit_area_style",
        "_connect_theme_signal",
        "_on_theme_changed",
        "background-color: #404040",
        "color: #E0E0E0",
        "ThemeManager"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ 表格编辑区域修复不完整，缺少: {missing_changes}")
        return False
    else:
        print("✅ 表格编辑区域深色主题样式修复完成")
        return True

def check_corner_button_theme_switch():
    """检查corner button主题切换修复"""
    table_file = "controller/system_plugin/edit/view/component/table/Table.py"

    if not os.path.exists(table_file):
        print(f"❌ Table.py文件不存在: {table_file}")
        return False

    with open(table_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查浅色主题corner button样式
    required_changes = [
        "# 浅色主题的corner button样式",
        "background-color: #F5F5F5",
        "border: 1px solid #D0D0D0",
        "re.sub(r'QTableCornerButton::section"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ Corner button主题切换修复不完整，缺少: {missing_changes}")
        return False
    else:
        print("✅ Corner button主题切换修复完成")
        return True

def check_execution_status_bar_fixes():
    """检查执行状态栏修复"""
    btn_action_file = "controller/test_runner/BtnAction.py"
    run_plugin_file = "controller/system_plugin/run/RunPlugin.py"

    results = []

    # 检查BtnAction.py
    if not os.path.exists(btn_action_file):
        print(f"❌ BtnAction.py文件不存在: {btn_action_file}")
        results.append(False)
    else:
        with open(btn_action_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "background-color: #404040; color: #E0E0E0",
            "background-color: #2E7D32; color: #E8F5E8",
            "background-color: #C62828; color: #FFEBEE",
            "ThemeManager"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ BtnAction.py执行状态栏修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ BtnAction.py执行状态栏深色主题修复完成")
            results.append(True)

    # 检查RunPlugin.py
    if not os.path.exists(run_plugin_file):
        print(f"❌ RunPlugin.py文件不存在: {run_plugin_file}")
        results.append(False)
    else:
        with open(run_plugin_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "# 根据主题设置初始样式",
            "background-color: #404040; color: #E0E0E0",
            "ThemeManager"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ RunPlugin.py执行状态栏修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ RunPlugin.py执行状态栏深色主题修复完成")
            results.append(True)

    return all(results)

def check_dark_orange_theme_table_styles():
    """检查深色橙色主题表格样式"""
    dark_orange_file = "resources/qss/dark_orange.qss"

    if not os.path.exists(dark_orange_file):
        print(f"❌ 深色橙色主题文件不存在: {dark_orange_file}")
        return False

    with open(dark_orange_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查表格样式
    required_styles = [
        "/* 表格样式 - 深色橙色主题 */",
        "QTableWidget::item",
        "background-color: #323232",
        "color: #b1b1b1",
        "QTableCornerButton::section"
    ]

    missing_styles = []
    for style in required_styles:
        if style not in content:
            missing_styles.append(style)

    if missing_styles:
        print(f"❌ 深色橙色主题表格样式不完整，缺少: {missing_styles}")
        return False
    else:
        print("✅ 深色橙色主题表格样式修复完成")
        return True

def check_menu_restructure():
    """检查菜单重构"""
    menu_bar_file = "view/MenuBar.py"
    settings_plugin_file = "controller/system_plugin/settings/SettingsPlugin.py"
    help_plugin_file = "controller/system_plugin/help/HelpPlugin.py"
    tools_plugin_file = "controller/system_plugin/tools/ToolsPlugin.py"

    results = []

    # 检查MenuBar.py
    if not os.path.exists(menu_bar_file):
        print(f"❌ MenuBar.py文件不存在: {menu_bar_file}")
        results.append(False)
    else:
        with open(menu_bar_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "from controller.system_plugin.settings.SettingsPlugin import SettingsPlugin",
            "from controller.system_plugin.help.HelpPlugin import HelpPlugin",
            "SettingsPlugin(self._menu_bar).load()",
            "HelpPlugin(self._menu_bar).load()"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ MenuBar.py菜单重构不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ MenuBar.py菜单重构完成")
            results.append(True)

    # 检查SettingsPlugin.py
    if not os.path.exists(settings_plugin_file):
        print(f"❌ SettingsPlugin.py文件不存在: {settings_plugin_file}")
        results.append(False)
    else:
        print("✅ SettingsPlugin.py文件已创建")
        results.append(True)

    # 检查HelpPlugin.py
    if not os.path.exists(help_plugin_file):
        print(f"❌ HelpPlugin.py文件不存在: {help_plugin_file}")
        results.append(False)
    else:
        print("✅ HelpPlugin.py文件已创建")
        results.append(True)

    # 检查ToolsPlugin.py是否移除了偏好设置
    if not os.path.exists(tools_plugin_file):
        print(f"❌ ToolsPlugin.py文件不存在: {tools_plugin_file}")
        results.append(False)
    else:
        with open(tools_plugin_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否移除了偏好设置相关代码
        if "_add_item_preferences" in content or "_open_preferences_dialog" in content:
            print("❌ ToolsPlugin.py仍然包含偏好设置相关代码")
            results.append(False)
        else:
            print("✅ ToolsPlugin.py已移除偏好设置相关代码")
            results.append(True)

    return all(results)

def check_final_fixes():
    """检查最终修复"""
    settings_plugin_file = "controller/system_plugin/settings/SettingsPlugin.py"
    help_plugin_file = "controller/system_plugin/help/HelpPlugin.py"
    main_frame_file = "view/MainFrame.py"
    config_file = "config.ini"

    results = []

    # 检查设置插件的偏好设置修复
    if not os.path.exists(settings_plugin_file):
        print(f"❌ SettingsPlugin.py文件不存在: {settings_plugin_file}")
        results.append(False)
    else:
        with open(settings_plugin_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "try:",
            "except Exception as e:",
            "print(f\"打开偏好设置对话框失败: {e}\")",
            "from functools import partial"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ SettingsPlugin.py偏好设置修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ SettingsPlugin.py偏好设置响应修复完成")
            results.append(True)

    # 检查帮助插件的检查更新功能
    if not os.path.exists(help_plugin_file):
        print(f"❌ HelpPlugin.py文件不存在: {help_plugin_file}")
        results.append(False)
    else:
        with open(help_plugin_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "_add_item_check_update",
            "检查更新",
            "show_update_dialog",
            "UpdateDialog",
            "CheckUpdateThread"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ HelpPlugin.py检查更新功能不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ HelpPlugin.py检查更新功能添加完成")
            results.append(True)

    # 检查MainFrame的主题启动修复
    if not os.path.exists(main_frame_file):
        print(f"❌ MainFrame.py文件不存在: {main_frame_file}")
        results.append(False)
    else:
        with open(main_frame_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "from controller.system_plugin.style.ThemeManager import ThemeManager",
            "theme_manager.apply_saved_theme()",
            "应用保存的主题而不是默认主题"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ MainFrame.py主题启动修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ MainFrame.py程序启动主题刷新修复完成")
            results.append(True)

    # 检查config.ini中help插件是否被移除
    if not os.path.exists(config_file):
        print(f"❌ config.ini文件不存在: {config_file}")
        results.append(False)
    else:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        if "PLUGIN=help" in content:
            print("❌ config.ini中仍然包含help插件配置")
            results.append(False)
        else:
            print("✅ config.ini中help插件配置已移除")
            results.append(True)

    return all(results)

def check_table_colorization_fixes():
    """检查表格文字染色修复"""
    colorizer_file = "controller/system_plugin/edit/view/component/table/Colorizer.py"
    theme_manager_file = "controller/system_plugin/style/ThemeManager.py"

    results = []

    # 检查Colorizer.py的修改
    if not os.path.exists(colorizer_file):
        print(f"❌ Colorizer.py文件不存在: {colorizer_file}")
        results.append(False)
    else:
        with open(colorizer_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "_get_theme_colors",
            "refresh_theme_colors",
            "ThemeManager",
            "dark_orange",
            "浅蓝色关键字",
            "浅绿色变量"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ Colorizer.py表格染色修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ Colorizer.py表格文字染色修复完成")
            results.append(True)

    # 检查ThemeManager.py的修改
    if not os.path.exists(theme_manager_file):
        print(f"❌ ThemeManager.py文件不存在: {theme_manager_file}")
        results.append(False)
    else:
        with open(theme_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()

        required_changes = [
            "notify_colorization_settings",
            "已通知表格颜色设置刷新"
        ]

        missing_changes = []
        for change in required_changes:
            if change not in content:
                missing_changes.append(change)

        if missing_changes:
            print(f"❌ ThemeManager.py表格染色通知修复不完整，缺少: {missing_changes}")
            results.append(False)
        else:
            print("✅ ThemeManager.py表格染色通知修复完成")
            results.append(True)

    return all(results)

def check_preferences_dialog_fixes():
    """检查偏好设置对话框修复"""
    settings_plugin_file = "controller/system_plugin/settings/SettingsPlugin.py"

    if not os.path.exists(settings_plugin_file):
        print(f"❌ SettingsPlugin.py文件不存在: {settings_plugin_file}")
        return False

    with open(settings_plugin_file, 'r', encoding='utf-8') as f:
        content = f.read()

    required_changes = [
        "开始打开偏好设置对话框",
        "_create_simple_preferences_dialog",
        "Preferences类导入成功",
        "简单偏好设置对话框已打开"
    ]

    missing_changes = []
    for change in required_changes:
        if change not in content:
            missing_changes.append(change)

    if missing_changes:
        print(f"❌ SettingsPlugin.py偏好设置对话框修复不完整，缺少: {missing_changes}")
        return False
    else:
        print("✅ SettingsPlugin.py偏好设置对话框响应修复完成")
        return True

def main():
    print("🔍 验证深色主题修复...")
    print("=" * 50)

    results = []

    # 检查各个文件的修改
    results.append(check_css_file())
    results.append(check_table_py())
    results.append(check_line_edit_area_py())
    results.append(check_status_bar_fixes())
    results.append(check_tags_fixes())
    results.append(check_light_theme_corner_button())
    results.append(check_table_edit_area_fixes())
    results.append(check_corner_button_theme_switch())
    results.append(check_execution_status_bar_fixes())
    results.append(check_dark_orange_theme_table_styles())
    results.append(check_menu_restructure())
    results.append(check_final_fixes())
    results.append(check_table_colorization_fixes())
    results.append(check_preferences_dialog_fixes())

    print("=" * 50)

    if all(results):
        print("🎉 所有修改都已正确应用！")
        print("\n修改总结:")
        print("1. ✅ 深色主题CSS文件已添加corner button样式")
        print("2. ✅ Table.py已添加主题切换支持和corner button样式设置")
        print("3. ✅ LineEditArea.py已添加深色主题输入框样式")
        print("4. ✅ 状态栏深色主题文字颜色修复完成")
        print("5. ✅ Tags组件深色主题样式修复完成")
        print("6. ✅ 浅色主题corner button样式修复完成")
        print("7. ✅ 表格编辑区域深色主题样式修复完成")
        print("8. ✅ Corner button主题切换修复完成")
        print("9. ✅ 执行页签状态栏深色主题修复完成")
        print("10. ✅ 深色橙色主题表格样式修复完成")
        print("11. ✅ 菜单重构完成")
        print("12. ✅ 偏好设置响应修复完成")
        print("13. ✅ 检查更新功能添加完成")
        print("14. ✅ 程序启动主题刷新修复完成")
        print("15. ✅ 表格文字染色修复完成")
        print("16. ✅ 偏好设置对话框响应增强完成")
        print("\n现在的修复效果:")
        print("【深色主题下】")
        print("- 表格的行列编号背景色为深色 (#3C3C3C)")
        print("- 表格左上角corner button背景色为深色 (#3C3C3C)")
        print("- Arguments等输入框有内容时背景色为深色 (#404040)")
        print("- 状态栏文字颜色为浅色 (#E0E0E0)，清晰可见")
        print("- Tags表格背景色为深色 (#404040)，文字颜色为浅色")
        print("- 表格编辑区域背景色为深色 (#404040)，文字颜色为浅色")
        print("- 执行页签状态栏背景色为深色 (#404040)，文字颜色为浅色")
        print("- 执行成功时状态栏为深绿色 (#2E7D32)，失败时为深红色 (#C62828)")
        print("【深色橙色主题下】")
        print("- 表格单元格背景色统一为深色 (#323232)")
        print("- 表格文字颜色为浅色 (#b1b1b1)")
        print("- 选中单元格为橙色渐变效果")
        print("- 关键字染色为浅蓝色 (#66D9EF)")
        print("- 变量染色为浅绿色 (#A6E22E)")
        print("- 注释染色为灰色 (#75715E)")
        print("【深色主题下】")
        print("- 关键字染色为蓝色 (#4A90E2)")
        print("- 变量染色为绿色 (#98C379)")
        print("- 注释染色为灰色 (#808080)")
        print("【浅色主题下】")
        print("- 表格左上角corner button背景色为浅色 (#F5F5F5)")
        print("- 表格编辑区域背景色为浅灰色 (#ececec)")
        print("- 执行页签状态栏背景色为浅灰色")
        print("【菜单结构】")
        print("- 新增'设置'菜单，包含'偏好设置'")
        print("- '帮助'菜单替代'简介'菜单，包含'关于RFCode'和'产品说明'")
        print("- '工具'菜单移除了'偏好设置'")
        print("【功能修复】")
        print("- 设置菜单下的偏好设置选项可以正常响应点击")
        print("- 帮助菜单包含检查更新功能，可以检查软件更新")
        print("- 程序启动时会自动应用保存的主题设置")
        print("- 移除了原有的help插件，避免菜单冲突")
        print("- 表格文字根据主题动态染色，关键字、变量、注释等有不同颜色")
        print("- 偏好设置对话框增强了错误处理和调试信息")
        print("【通用】")
        print("- 支持实时主题切换，corner button会正确切换颜色")
        print("- 执行状态栏根据执行结果显示不同颜色")
        return True
    else:
        print("❌ 部分修改未正确应用，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
