'''
Created on 2019年10月31日

@author: 10247557,10225935,10259183
'''
from PyQt5.QtWidgets import QTextEdit
from PyQt5.QtGui import QTextCursor


class LogTraceTextEditor(object):

    def __init__(self):
        '''
        Constructor
        '''
        self._text_editor = QTextEdit()

    def get_editor(self):
        return self._text_editor

    def append(self, text):
        """Append text to the QTextEdit."""
        cursor = self._text_editor.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(text)
        self._text_editor.setTextCursor(cursor)
        self._text_editor.ensureCursorVisible()
