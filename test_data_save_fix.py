#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据保存修复
验证用户修改Setup等文本框内容后，切换页签再返回时内容是否保持
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer

def test_line_edit_area_save():
    """测试LineEditArea数据保存功能"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        
        # 创建一个模拟的父窗口
        parent = QWidget()
        
        # 创建Setup输入框
        setup_area = LineEditArea(parent, 'Setup')
        setup_area.load()
        
        print("✅ LineEditArea实例创建成功")
        
        # 模拟初始数据填充
        initial_data = 'Open Browser    chrome'
        print(f"\n=== 初始数据填充 ===")
        setup_area.fill_data(initial_data)
        print(f"Setup初始填充: {initial_data}")
        
        # 等待一段时间，让垂直居中完成
        import time
        time.sleep(0.1)
        
        # 获取初始填充后的数据
        initial_result = setup_area.get_data()
        print(f"初始获取的数据: {initial_result}")
        
        # 模拟用户修改数据（通过直接设置文本模拟用户在对话框中的修改）
        modified_data = 'Open Browser    firefox    --headless'
        print(f"\n=== 模拟用户修改数据 ===")
        
        # 临时断开信号，模拟用户在对话框中修改
        setup_area._line.textChanged.disconnect(setup_area._modify_data)
        setup_area._line.setPlainText(modified_data)
        setup_area._line.textChanged.connect(setup_area._modify_data)
        
        # 触发_modify_data，模拟用户点击确定
        setup_area._modify_data()
        print(f"用户修改为: {modified_data}")
        
        # 获取修改后的数据
        modified_result = setup_area.get_data()
        print(f"修改后获取的数据: {modified_result}")
        
        # 检查内部状态是否更新
        print(f"内部_text状态: {setup_area._text}")
        
        # 模拟切换页签再返回（重新调用fill_data）
        print(f"\n=== 模拟切换页签再返回 ===")
        # 这里我们用相同的数据调用fill_data，模拟系统重新加载
        setup_area.fill_data(initial_data)  # 系统尝试用初始数据重新填充
        print(f"系统尝试重新填充初始数据: {initial_data}")
        
        # 等待一段时间
        time.sleep(0.1)
        
        # 获取最终数据
        final_result = setup_area.get_data()
        print(f"最终获取的数据: {final_result}")
        
        # 检查数据是否保持用户的修改
        print(f"\n=== 数据保存验证 ===")
        data_preserved = (final_result == modified_result)
        print(f"用户修改是否保持: {'✅' if data_preserved else '❌'}")
        
        if data_preserved:
            print("✅ 数据保存功能正常，用户修改得到保持")
        else:
            print("❌ 数据保存功能异常，用户修改被覆盖")
            print(f"期望: {modified_result}")
            print(f"实际: {final_result}")
        
        return {
            'setup_area': setup_area,
            'data_preserved': data_preserved,
            'initial_data': initial_data,
            'modified_data': modified_data,
            'final_result': final_result
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("数据保存修复测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
数据保存修复测试

问题: 在编辑页签修改Setup文本框内容后点确定，然后切换到文本编辑再返回编辑页签，
Setup文本框的内容又被重置了，编辑的内容未保存

原因: 
1. _modify_data方法保存数据后没有更新self._text状态
2. fill_data方法没有检查数据是否已经是最新的，总是重新填充

修复: 
1. 在_modify_data中更新self._text状态
2. 在fill_data中检查数据是否需要更新，避免覆盖用户修改

测试: 点击按钮测试用户修改后数据是否能正确保存
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试数据保存功能")
    layout.addWidget(test_btn)
    
    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)
    
    # 测试结果
    test_result = None
    
    def run_test():
        output_text.clear()
        output_text.append("=== 开始数据保存测试 ===")
        
        nonlocal test_result
        test_result = test_line_edit_area_save()
        
        if test_result:
            if test_result['data_preserved']:
                output_text.append("✅ 数据保存测试通过")
                output_text.append("用户修改的内容在切换页签后得到保持")
                output_text.append(f"初始数据: {test_result['initial_data']}")
                output_text.append(f"用户修改: {test_result['modified_data']}")
                output_text.append(f"最终结果: {test_result['final_result']}")
            else:
                output_text.append("❌ 数据保存测试失败")
                output_text.append("用户修改的内容在切换页签后丢失")
                output_text.append(f"初始数据: {test_result['initial_data']}")
                output_text.append(f"用户修改: {test_result['modified_data']}")
                output_text.append(f"最终结果: {test_result['final_result']}")
            
            output_text.append("\n=== 测试完成 ===")
            output_text.append("如果测试通过，说明数据保存功能修复成功")
        else:
            output_text.append("❌ 测试执行失败")
    
    test_btn.clicked.connect(run_test)
    
    # 自动运行测试
    QTimer.singleShot(1000, run_test)
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 数据保存修复测试...")
    print("=" * 60)
    
    # 运行控制台测试
    test_line_edit_area_save()
    
    print("\n" + "=" * 60)
    print("启动GUI测试窗口...")
    
    sys.exit(main())
