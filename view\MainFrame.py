'''
Create on  2019年10月14日


@author:
'''

import os, subprocess, time
import sys
import json
import codecs
import platform
from PyQt5 import QtWidgets
from PyQt5.Qt import Qt, QIcon
from PyQt5.QtWidgets import QDesktopWidget, QMainWindow, QHBoxLayout, QApplication
import requests
from threading import Thread

sys.path.append(os.path.abspath('%s/..' % sys.path[0]))
# 将项目目录添加到 sys.path 的最前面
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
robot_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(robot_root)
from settings.SystemSettings import SystemSettings
if hasattr(sys, 'frozen'):
    os.environ['PATH'] = sys._MEIPASS + ";" + os.environ['PATH']

def _set_python_version():
    python_version = get_system_default_python()
    if  python_version == 'None':
        app = QApplication(sys.argv)
        dialog = QtWidgets.QDialog()
        dialog.setWindowTitle("选择Python版本")
        layout = QtWidgets.QVBoxLayout()

        radio_2 = QtWidgets.QRadioButton("Python 2")
        radio_3 = QtWidgets.QRadioButton("Python 3")
        layout.addWidget(radio_2)
        layout.addWidget(radio_3)

        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        dialog.setLayout(layout)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            if radio_2.isChecked():
                python_version = '2'
            elif radio_3.isChecked():
                python_version = '3'
    if python_version.startswith('3'):
        SystemSettings().write('PYTHON_VER', '3')
        SystemSettings().write('ROBOT_VERSION', 'robot')
    else:
        SystemSettings().write('PYTHON_VER', '2')
        SystemSettings().write('ROBOT_VERSION', 'robot2')
    print("python version is {}".format(python_version))
    print("robot version is {}".format(SystemSettings().read('ROBOT_VERSION')))

def get_system_default_python():
    try:
        # 调用系统命令获取默认 Python 版本
        result = subprocess.run(
            ["python", "--version"],
            capture_output=True,
            text=True,
            check=True
        )
        print("python --version")
        print('result.stdout')
        print(str(result.stdout))
        if 'Python' in str(result.stdout):
            # 提取版本号
            version = result.stdout.split()[1]
            print("2 python version is {}".format(version))
            return version
        else:
            # 如果没有找到 Python 版本信息，尝试使用 python3
            result = subprocess.run(
                ["python3", "--version"],
                capture_output=True,
                text=True,
                check=True
            )
            print("python3 --version")
            print('result.stdout')
            print(str(result.stdout))
            if 'Python' in str(result.stdout):
                version = result.stdout.split()[1]
                print("3 python version is {}".format(version))
                return version
            else:
                return 'None'
    except FileNotFoundError:
        return 'None'
    except subprocess.CalledProcessError as e:
        return 'None'

_set_python_version()


from controller.system_plugin.SignalDistributor import SignalDistributor
from model.data_file.DataFile import SaveSequence
from resources.Loader import Loader
from plugins.SystemLibrary import SystemLibrary
from settings.i18n.Loader import LanguageLoader
from utility.ModifiedItemRepository import ModifiedItemRepository
from utility.PluginRepository import PluginRepository
from view.MenuBar import MenuBar
from view.StatusBar import StatusBar
from view.ToolBar import ToolBar
from view.common.MessageBox import MessageBox
from view.edit_area.EditFrame import EditFrame
from view.explorer.ProjectExplorer import ProjectExplorer
from settings.HistoryProject import HistoryProject
from utility.UIRepository import UIRepository
from controller.userinfo.UserInfoManager import UserInfoManager
from update.RFCODEUpdater import RFCODEUpdater
from robot2.parsing.model import *
from robot2.parsing.populators import *
from robot2.parsing.robotreader import *
from robot2.parsing.tsvreader import *
from robot2.utils.utf8reader import *
from robot.variables import *




#VERSION_URL = "http://10.7.213.183:31381/version?operatingSystem={}"
VERSION_URL = "http://10.2.71.213:31381/version?operatingSystem={}"
#VERSION_URL = "http://127.0.0.1:31381/version?operatingSystem={}"



class MainFrame(QMainWindow):

    def __init__(self):
        super().__init__()
        self._init_ui()

    def _init_ui(self):
        self._set_title()
        self._set_logo()
        self._set_geometry()
        self._load_menu_bar()
        self._load_status_bar()
        self._load_tool_bar()
        self._load_project_explorer()
        self._load_edit_frame()
        self._set_layout()
        self._set_global_style()
        UIRepository().update('MainFrame', self)

    def _set_title(self):
        self.setWindowTitle(LanguageLoader().get('LOGO_TEXT'))

    def _set_logo(self):
        path = Loader().get_path('LOGO')
        if not os.path.exists(path):
            MessageBox(self).show_warning(LanguageLoader().get('LOGO_IS_NOT_EXIST'))
        self.setWindowIcon(QIcon(path))

    def _set_geometry(self):
        self.setWindowState(Qt.WindowMaximized)

    def _set_layout(self):
        self.setLayout(QHBoxLayout())
        self._center()
        self.addDockWidget(Qt.LeftDockWidgetArea, self._project_explorer.get_widget())

    def _center(self):
        frame = self.frameGeometry()
        frame.moveCenter(QDesktopWidget().availableGeometry().center())
        self.move(frame.topLeft())

    def _load_menu_bar(self):
        MenuBar(self).load()

    def _load_tool_bar(self):
        self._tool_bar = ToolBar(self)
        self._tool_bar.load()

    def simulate_home_button_click(self):
        self._tool_bar.simulate_home_button_click()

    def _load_project_explorer(self):
        self._project_explorer = ProjectExplorer(self)
        PluginRepository().add('PROJECT_EXPLORER', self._project_explorer)
        self._project_explorer.load()

    def _load_edit_frame(self):
        self._edit_frame = EditFrame(self)
        PluginRepository().add('EDIT_FRAME', self._project_explorer)
        self._edit_frame.load()

    def _load_status_bar(self):
        StatusBar(self)

    def _set_global_style(self):
        PluginRepository().add("MAIN_FRAME", self)

        # 应用保存的主题而不是默认主题
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.apply_saved_theme()
            print(f"应用保存的主题: {theme_manager.get_current_theme()}")
        except Exception as e:
            print(f"应用保存主题失败，使用默认主题: {e}")
            # 如果主题管理器失败，使用默认样式
            style_file = Loader().get_path('DEFAULT_QSS')
            with codecs.open(style_file, "r", encoding='utf-8') as f:
                style = f.read()
            self.setStyleSheet(style)

    def closeEvent(self, event):
        HistoryProject.write('LAST_CLICKED_PATH', self._project_explorer.lastClickItemPath)
        SignalDistributor().emit_close_event()
        SaveSequence.save_all()
        if ModifiedItemRepository().find('MODIFIED_ITEM'):
            reply = MessageBox().show_warning_ignore(LanguageLoader().get('CLOSE_TIPS'))
            if reply == QtWidgets.QMessageBox.Yes: # 直接保存
                SignalDistributor().save_all()
            elif reply == QtWidgets.QMessageBox.Ignore:
                event.ignore()
            elif reply == QtWidgets.QMessageBox.No:
                event.accept()

def except_hook(cls, exception, traceback):
    sys.__excepthook__(cls, exception, traceback)

def check_version():
    try:
        operatingSystem = platform.architecture()[0]
        content = json.loads(requests.get(VERSION_URL.format(operatingSystem)).text)
        print(content)
        version = content.get("version")
        local = SystemLibrary.get_current_version()
        if version > local:
            reply = MessageBox().show_information('本地版本为：' + local + '，当前服务器最新版本为： ' + version + "，是否升级到最新版本？")
            if reply == QtWidgets.QMessageBox.Yes:
                update = UpdateThread(content.get("path"))
                update.start()
            elif reply == QtWidgets.QMessageBox.No:
                pass
    except Exception:
        traceback.print_exc()

class UpdateThread(Thread):
    def __init__(self, down_load_path):
        super().__init__()
        self.down_load_path = down_load_path

    def run(self):
        resp = requests.get(self.down_load_path)
        name = self.down_load_path.split('/')[-1]
        file_path = "{}/tools/{}".format(os.path.abspath(os.path.dirname(__file__)), name)
        print("down_load_path", file_path)
        if resp.status_code == 200:
            with open(file_path, "wb") as file:
                file.write(resp.content)
            print("update file is ready")
            operatingType = platform.system()
            operatingSystem = platform.architecture()[0]
            print('operatingType is {}'.format(operatingType))
            print('operatingSystem is {}'.format(operatingSystem))
            if operatingType == "Windows":
                self.update_windows(file_path, operatingSystem)
            elif operatingType == "Linux":
                self.update_linux(file_path)
            else:
                print("not support")

    def update_windows(self, file_path, operatingSystem):
        tools_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools'))
        updater_name = "RFCODEUpdater_32.exe" if operatingSystem == "32bit" else "RFCODEUpdater.exe"
        updater_path = os.path.join(tools_dir, updater_name)
        if not os.path.exists(updater_path):
            raise FileNotFoundError(f"Updater not found at {updater_path}")
        update_cmd = f'cd "{tools_dir}" && "{updater_path}" "{file_path}"'
        print(f'start update:{update_cmd}')
        os.system(update_cmd)

    def update_linux(self, file_path):
        pass

if __name__ == '__main__':
    import cgitb
    import traceback
    cgitb.enable(format = 'text')
    sys.excepthook = except_hook
    app = QApplication(sys.argv)
    try:
        ui = MainFrame()
        ui.show()
        check_version()
        UserInfoManager().start()
    except:
        traceback.print_exc()
        input("press any key to exit")
    sys.exit(app.exec_())



