'''
Created on 2019年12月17日

@author: 10240349
'''
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserF<PERSON>ry
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.ModifiedItemRepository import ModifiedItemRepository
from utility.PluginRepository import PluginRepository
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.PyItem import PyItem
from view.explorer.tree_item.ResourceItem import ResourceItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.TestcaseItem import TestcaseItem
from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
from view.explorer.tree_item.VariableItem import VariableItem
from view.common.MessageBox import MessageBox


EDIT = LanguageLoader().get('EDIT')
TEXT_EDIT = LanguageLoader().get('TEXT_EDIT')
RUN = LanguageLoader().get('RUN')


class Save(object):

    @staticmethod
    def save_all():
        _list = ModifiedItemRepository().find('MODIFIED_ITEM')
        if _list:
            modified_item = [item for item in _list]
            Save._save_modified_item(modified_item)

    @staticmethod
    def _save_modified_item(modified_item):
        edit_frame = PluginRepository().find('EDIT_TAB')
        if modified_item:
            if edit_frame.tabText(edit_frame.currentIndex()) == TEXT_EDIT:
                Save._save_current_item_before_press_btn()
            for item in modified_item:
                Save._save_item(item)

    @staticmethod
    def _save_current_item_before_press_btn():
        current_item = CurrentItem().get_current_item()
        current_file_screen_content = PluginRepository().find('TEXT_EDIT').text()
        data_file_obj = Save._get_data_file(current_item)
        data_file_obj.update(current_file_screen_content)

    @staticmethod
    def save():
        item = CurrentItem().get_current_item()
        Save._save_item(item)
        SignalDistributor().format_save()

    @staticmethod
    def _save_item(item):
        parent = item.parent()
        edit_frame = PluginRepository().find('EDIT_TAB')
        if edit_frame.tabText(edit_frame.currentIndex()) == TEXT_EDIT:
            Save._save_text_editor_file(item)
        elif edit_frame.tabText(edit_frame.currentIndex()) == EDIT:
            Save._save_editor_file(item)
        elif edit_frame.tabText(edit_frame.currentIndex()) == RUN:
            Save._save_run_file(item)
        if ModifiedItemRepository().find('MODIFIED_ITEM'):
            Save._remove_modified_item(item, parent, ModifiedItemRepository().find('MODIFIED_ITEM'))  # Remove the modified item from the repository after saving
        file_write_check = PluginRepository().find('FILE_WRITE_CHECK')
        if file_write_check and 'Permission' in file_write_check[0]:
            MessageBox().show_critical('no permission to write: '+PluginRepository().find('FILE_WRITE_CHECK')[1])

    @staticmethod
    def _clear_testcase(item):
        try:
            _list = item.get_child_list(TestcaseItem)
            for testcase in _list:
                ExecutiveTestCaseRepository().delete_testcase('EXECUTIVE_TESTCASES', testcase)
        except:
            pass

    @staticmethod
    def _save_text_editor_file(item):
        parent = item.parent()
        SignalDistributor().del_star_modify(item)
        current_item = CurrentItem().get_current_item()
        if current_item == item or current_item.parent() == item:
            Save._save_current_item(item, parent)
        else:
            Save._save_other_item(item, parent)

    @staticmethod
    def _save_current_item(item, parent):
        current_file_screen_content = PluginRepository().find('TEXT_EDIT').text()
        if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
            Save._save_data_file_with_update(item, current_file_screen_content)
        else:
            Save._save_data_file_with_update(parent, current_file_screen_content)

    @staticmethod
    def _save_other_item(item, parent):
        if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
            Save._save_data_file_without_update(item)
        elif isinstance(item, TestcaseItem) or isinstance(item, UserKeywordItem) or isinstance(item, VariableItem):
            Save._save_data_file_without_update(parent)

    @staticmethod
    def _save_data_file_with_update(item, current_file_screen_content):
        data_file_obj = Save._get_data_file(item)
        data_file_obj.update(current_file_screen_content)
        data_file_obj.save()
        item.refresh_children()

    @staticmethod
    def _save_data_file_without_update(item):
        Save._save_without_refresh(item)
        item.refresh_children()

    @staticmethod
    def _get_data_file(item):
        parsed_item = ItemParserFacory().create(type(item).__name__ + 'Parser')
        return parsed_item.get_cur_data_file(item)

    @staticmethod
    def _remove_modified_item(item, parent, _list):
        if isinstance(item, SuiteItem) or isinstance(item, ResourceItem) \
                or isinstance(item, ProjectTreeItem) or isinstance(item, PyItem):
            if item in _list:
                _list.remove(item)
        else:
            if parent in _list:
                _list.remove(parent)  # 此时不能使用item.parent()获取父节点，因为item已保存，故item.parent()不是之前的父亲，故只能使用保存之前的父亲
        ModifiedItemRepository().update('MODIFIED_ITEM', _list)

    @staticmethod
    def _save_editor_file(item):
        """
        Save the editor file and remove the star modification indicator.
        
        Args:
            item: The item to be saved.
        """
        Save._save_without_refresh(item)
        SignalDistributor().del_star_modify(item)

    @staticmethod
    def _save_run_file(item):
        Save._save_without_refresh(item)
        SignalDistributor().del_star_modify(item)
        SignalDistributor().format_save()

    @staticmethod
    def _save_without_refresh(item):
        data_file_obj = Save._get_data_file(item)
        data_file_obj.save()
