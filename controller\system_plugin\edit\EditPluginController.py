# encoding=utf-8
'''
Created on 2019年11月12日

@author: 10247557
'''

from _functools import partial
import time
import traceback

from PyQt5.Qt import Qt
from PyQt5.QtWidgets import QWidget

from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.table.Table import _table
from model.CurrentItem import CurrentItem
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.explorer.tree_item.PyItem import PyItem
from settings.SystemConfig import RF_ASSISTANT_SLOT

EDIT = LanguageLoader().get('EDIT')
TEXT_EDIT = LanguageLoader().get('TEXT_EDIT')
RUN = LanguageLoader().get('RUN')
BASH = LanguageLoader().get('BASH')
RF_ASSISTANT = LanguageLoader().get('RF_ASSISTANT')


class EditPluginController(object):

    def __init__(self, tab_obj, parent):
        self._tab_obj = tab_obj
        self._parent = parent
        self._edit_tab = None
        self._last_click_tab = EDIT
        self._tab_obj.tabBarClicked['int'].connect(partial(self._tab_click, self))
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.show_item.connect(self._show_editor, Qt.QueuedConnection)
        self._signal_distributor.show_item.connect(self._set_edit_widget)

    @staticmethod
    def _tab_click(this, index):
        if PluginRepository().find('LAST_CLICK_TAB'):
            this._last_click_tab = PluginRepository().find('LAST_CLICK_TAB')
#         if this._last_click_tab == EDIT and this._tab_obj.tabText(index) != EDIT:  # 从edit页面切换到其它页面
#             try:
#                 path = CurrentItem().get().get("path")
#                 if path:
#                     PluginRepository().find('TEXT_EDIT').load_file(path)
#             except Exception:
#                 traceback.print_exc()
        if this._tab_obj.tabText(index) == EDIT and this._last_click_tab != EDIT:  # 从其他页面切换到edit页面
            this._show_editor(CurrentItem().get_current_item(), index, 0)
        if this._last_click_tab == TEXT_EDIT and this._tab_obj.tabText(index) != TEXT_EDIT:  # 从textedit切换到其他页面
            if this._update_data_file(CurrentItem()):
                if isinstance(CurrentItem().get_current_item(), PyItem):
                    this._signal_distributor.update(CurrentItem().get_current_item(), CurrentItem().get_current_item())
                else:
                    this._signal_distributor.update(CurrentItem().get_current_item(), CurrentItem().get_current_item().parent())
        if this._last_click_tab != TEXT_EDIT and this._tab_obj.tabText(index) == TEXT_EDIT:  # 从其他页面切换到text edit页面
            try:
                path = CurrentItem().get().get("path")
                PluginRepository().find('TEXT_EDIT').load_file(path)
            except Exception:
                traceback.print_exc()
        if this._last_click_tab != BASH and this._tab_obj.tabText(index) == BASH:
            this._signal_distributor.start_bash_load()
        this._last_click_tab = this._tab_obj.tabText(index)

    @staticmethod
    def _show_editor(view_tree_item, is_add, current_index=None):
        LocalRepoUpdater(view_tree_item).update()
        edit_plugin_controller = UIRepository().find('edit_plugin_controller')
        if not view_tree_item:
            edit_plugin_controller.set_widget('BlankEdit', None)
        else:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.query(view_tree_item)
            edit_plugin_controller.set_widget(CurrentItem().get()['type'] + 'Edit', parsed_item, current_index)

    def _set_edit_widget(self, item, is_add):
        current_index = self._tab_obj.currentIndex()
        if is_add:
            if current_index == SystemSettings().get_value('TEXT_EDIT_SLOT'):
                self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))
        if current_index == SystemSettings().get_value('RUN_SLOT') or current_index == SystemSettings().get_value('RF_ASSISTANT_SLOT'):
            self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))

    @staticmethod
    def _update_data_file(text_edit_item):
        data_file = None
        view_tree_item = text_edit_item.get_current_item()
        if view_tree_item:
            data_file = ItemParserFacory().create(view_tree_item.__class__.__name__ + 'Parser').get_cur_data_file(view_tree_item)
        if not data_file:
            return False
#         current_item_path = text_edit_item.get().get("path")
        screen_content = PluginRepository().find('TEXT_EDIT').get()
        if screen_content != data_file.get_content():
            data_file.update(screen_content)
            return True
        return False

    def set_edit_tab(self, edit_tab):
        self._edit_tab = edit_tab

    def get_edit_tab(self):
        return self._edit_tab

    def set_widget(self, edit_type, parsed_item, current_index=None):
        if current_index is None:
            current_index = self._tab_obj.currentIndex()
        if self._edit_tab:
            self._tab_obj.removeTab(self._tab_obj.indexOf(self._edit_tab))
        self._edit_tab = QWidget()
        self._editor = self._get_editor(edit_type)
        self._edit_tab.setLayout(self._editor.load())
        self._tab_obj.insertTab(SystemSettings().get_value('EDIT_SLOT'), self._edit_tab, EDIT)
        if current_index == SystemSettings().get_value('EDIT_SLOT'):
            self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))
            if parsed_item:
                self.fill_data(parsed_item)
        return self._editor

    def _get_editor(self, edit_type):
        if edit_type == 'VariableItemEdit':
            if CurrentItem().get_parent():
                parent_type = CurrentItem().get_parent().split('Item')[0]
                editor = UIRepository().find(parent_type + edit_type)
            else:
                editor = UIRepository().find('BlankEdit')
        else:
            editor = UIRepository().find(edit_type)
        return editor

    def fill_data(self, parsed_item):
        self._editor.fill_data(parsed_item)
