# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     CurrentItem
   Description :
   Author :       10140129
   date：          2019/11/9
-------------------------------------------------
   Change Activity:
                   2019/11/9:
-------------------------------------------------
"""
from utility.Singleton import Singleton
# from view.explorer.tree_item.PyItem import PyItem
# from view.explorer.tree_item.TestcaseItem import TestcaseItem
# from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
# from view.explorer.tree_item.VariableItem import VariableItem


@Singleton
class CurrentItem(object):

    def __init__(self):
        self._set_default()

    def set(self, current_item):
        self._set_default()
        if current_item:
            self._current_item = current_item
            self._name = current_item.text(0)
            self._set_path()
            self._set_suite()
            self._type = type(current_item).__name__

    @property
    def name(self):
        if hasattr(self, "_name"):
            return self._name
        return None

    def _set_default(self):
        self._current_item = None
        self._name = None
        self._path = None
        self._suite = None
        self._type = 'Blank'
        self._index = None

    def _set_path(self):
        self._index = None
        if type(self._current_item).__name__ == 'TestcaseItem' or type(self._current_item).__name__ == 'UserKeywordItem' or \
                type(self._current_item).__name__ == 'VariableItem':
            path = (self._current_item.parent().get_path(), self._current_item.parent().get_child_index(self._current_item))
            self._path = path[0]
            self._index = path[1]
#         elif type(self._current_item).__name__ == 'PyItem':
# self._path = "{}/{}".format(self._current_item.parent().get_py_path(), self._current_item.text(0))
#             self._path = self._current_item.get_path()
        else:
            self._path = self._current_item.get_path()

    def _set_suite(self):
        if type(self._current_item).__name__ == 'TestcaseItem' or type(self._current_item).__name__ == 'UserKeywordItem' or \
                type(self._current_item).__name__ == 'VariableItem':
            if type(self._current_item.parent()).__name__ == 'SuiteItem':
                self._suite = self._current_item.parent()
        else:
            self._suite = None

    def get_parent(self):
        if hasattr(self, '_current_item'):
            return type(self._current_item.parent()).__name__
        else:
            return None

    def get(self):
        result = {"name": self._name, "path": self._path, "type": self._type, "suite": self._suite}
        if self._index is None:
            return result
        result.update({"index": self._index})
        return result

    def get_current_item(self):
        return self._current_item

if __name__ == "__main__":
    # CurrentItem().set("a","/media/b","suit")
    # print(CurrentItem().get())
    # CurrentItem().set("a", ("/media/b",1), "suit")
    print(type(None).__name__)
