# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from controller.system_plugin.rf_assistant.RF_Helper.api.RdcApi import RdcApi


class LoginDialogWindow(QDialog):
    loginDialogReady = pyqtSignal(dict) 

    def __init__(self, x, y, para={}, parent=None):
        super().__init__(parent)
        self.setWindowTitle("登录")
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setGeometry(x, y, 300, 180)  # 增加宽度和高度
        self.login_icon = QApplication.style().standardIcon(QStyle.SP_DialogApplyButton)
        self.setWindowIcon(self.login_icon)
        self.setStyleSheet("""
            QDialog {
                background-color: #f9f9f9;
                border-radius: 10px;
            }
            QLabel {
                font-size: 14px;
                color: #333;
            }
            QLineEdit {
                font-size: 14px;
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QGridLayout {
                margin: 20px;  /* 增加页边距 */
            }
        """)

        self.initUI(para)

    def initUI(self, para):
        # 账号和密码标签及输入框
        self.userkeywordDirLabel = QLabel('账号：')
        self.userkeywordDirEdit = QLineEdit(para.get('account', '123'))
        self.userkeywordDirEdit.setPlaceholderText("8位工号")
        
        self.testcaseDirLabel = QLabel('密码：')
        self.testcaseDirEdit = QLineEdit()
        self.testcaseDirEdit.setEchoMode(QLineEdit.Password)
        self.testcaseDirEdit.setPlaceholderText("人事在线密码")

        # 登录和取消按钮
        self.okButton = QPushButton("登录")
        self.cancelButton = QPushButton("取消")
        self.okButton.clicked.connect(self.on_ok_button_clicked)
        self.cancelButton.clicked.connect(self.on_cancel_button_clicked)

        # 布局
        grid = QGridLayout()
        grid.setSpacing(10)  # 增加控件之间的间距
        grid.setContentsMargins(30, 30, 30, 30)
        grid.addWidget(self.userkeywordDirLabel, 0, 0,)
        grid.addWidget(self.userkeywordDirEdit, 0, 1, 1, 2)
        grid.addWidget(self.testcaseDirLabel, 1, 0)
        grid.addWidget(self.testcaseDirEdit, 1, 1, 1, 2)
        grid.addWidget(self.okButton, 2, 1)
        grid.addWidget(self.cancelButton, 2, 2)

        self.setLayout(grid)

    def on_ok_button_clicked(self):
        account = self.userkeywordDirEdit.text()
        password = self.testcaseDirEdit.text()
        rdc = RdcApi({'account': account, 'password': password})
        token = rdc.token if rdc.tokenStatus == 1 else ''
        
        self.loginDialogReady.emit({'token': token, 'account': account})
        with open('account.ini', 'w') as f:
            f.write(str({'account': account, 'token': token}))
        self.accept()

    def on_cancel_button_clicked(self):
        self.reject()
