'''
Created on 2019年10月31日

@author: 10247557,10225935,10259183
'''
from PyQt5.QtWidgets import QTextEdit, QLineEdit
from iplatform.highlight.LogHighLighter import LogHighLighter


class LogTextEditor(object):

    def __init__(self):
        self._log_summary_editor = QTextEdit()
        self._log_summary_editor.setLineWrapMode(QTextEdit.NoWrap)
        self._log_trace_editor = QTextEdit()
        self._log_trace_editor.setLineWrapMode(QTextEdit.NoWrap)
#         self._log_summary_editor.moveCursor(QTextCursor.End)
        self._log_summary_editor.setReadOnly(True)
        self._log_summary_editor.highlighter = LogHighLighter(self._log_summary_editor)
        self._log_trace_editor.highlighter = LogHighLighter(self._log_trace_editor)
        self._progress_editor = QLineEdit()
        self._progress_editor.setReadOnly(True)

    def get_log_summary_editor(self):
        return self._log_summary_editor

    def get_log_trace_editor(self):
        return self._log_trace_editor

    def get_progress_editor(self):
        return self._progress_editor

