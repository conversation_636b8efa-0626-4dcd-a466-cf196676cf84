# encoding=utf-8
'''
Created on 2019年11月20日

@author: 10247557
'''
from model.CurrentItem import CurrentItem
from utility.Reflection import Reflection


class ItemParserFacory(object):

    @staticmethod
    def create(editor_class_parser, *args):
        if editor_class_parser == 'VariableItemParser':
            parent_type = CurrentItem().get_parent().split('Item')[0]
            obj =  Reflection().create_obj('controller.system_plugin.edit.parser.' + parent_type + editor_class_parser, parent_type + editor_class_parser, *args)
        else:
            obj =  Reflection().create_obj('controller.system_plugin.edit.parser.' + editor_class_parser, editor_class_parser, *args)
        return obj