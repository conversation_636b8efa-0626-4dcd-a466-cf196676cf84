# coding=utf-8
'''
Created on 2020年1月14日

@author: 10240349
'''
from PyQt5.QtWidgets import QCheckBox, QLabel

from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository


class PrintTraceLogSwitch(object):

    def _set_trace_log_switch(self, layout):
        self._print_trace_log_check = QCheckBox()
        PluginRepository().add('TRACE_LOG_CHECK', self._print_trace_log_check)
        layout.addWidget(self._print_trace_log_check)
        label = QLabel(LanguageLoader().get('TRACE_LOG_SWITCH'))
        layout.addWidget(label)

    def is_open(self):
        if PluginRepository().find('TRACE_LOG_CHECK').isChecked():
            return False
        else:
            return True
