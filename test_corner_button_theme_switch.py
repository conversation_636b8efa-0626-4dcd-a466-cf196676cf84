#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试表格corner button主题切换修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QHBoxLayout)
from PyQt5.QtCore import Qt

def create_test_table():
    """创建测试表格"""
    table = QTableWidget(5, 5)
    table.setHorizontalHeaderLabels(['列1', '列2', '列3', '列4', '列5'])
    
    # 填充一些测试数据
    for row in range(5):
        for col in range(5):
            item = QTableWidgetItem(f"单元格({row},{col})")
            table.setItem(row, col, item)
    
    return table

def apply_light_theme_table_styles(table):
    """应用浅色主题表格样式"""
    table_style = """
        QTableWidget {
            background-color: #FFFFFF;
            color: #000000;
            gridline-color: #D0D0D0;
            border: 1px solid #D0D0D0;
        }
        QHeaderView::section {
            background-color: #F5F5F5;
            color: #000000;
            border: 1px solid #D0D0D0;
            padding: 4px;
        }
        QTableCornerButton::section {
            background-color: #F5F5F5;
            border: 1px solid #D0D0D0;
        }
        QAbstractScrollArea::corner {
            background-color: #F5F5F5;
            border: 1px solid #D0D0D0;
        }
    """
    table.setStyleSheet(table_style)
    
    # 程序化设置corner button
    try:
        from PyQt5.QtWidgets import QPushButton
        corner_button = table.findChild(QPushButton)
        if corner_button:
            corner_button.setStyleSheet("background-color: #F5F5F5; border: 1px solid #D0D0D0;")
    except Exception as e:
        print(f"设置浅色主题corner button失败: {e}")

def apply_dark_theme_table_styles(table):
    """应用深色主题表格样式"""
    table_style = """
        QTableWidget {
            background-color: #404040;
            color: #E0E0E0;
            gridline-color: #555555;
            border: 1px solid #555555;
        }
        QHeaderView::section {
            background-color: #3C3C3C;
            color: #E0E0E0;
            border: 1px solid #555555;
            padding: 4px;
        }
        QTableCornerButton::section {
            background-color: #3C3C3C;
            border: 1px solid #555555;
        }
        QAbstractScrollArea::corner {
            background-color: #3C3C3C;
            border: 1px solid #555555;
        }
    """
    table.setStyleSheet(table_style)
    
    # 程序化设置corner button
    try:
        from PyQt5.QtWidgets import QPushButton
        corner_button = table.findChild(QPushButton)
        if corner_button:
            corner_button.setStyleSheet("background-color: #3C3C3C; border: 1px solid #555555;")
    except Exception as e:
        print(f"设置深色主题corner button失败: {e}")

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("表格Corner Button主题切换测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试表格corner button在主题切换时的颜色变化")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建控制按钮
    button_layout = QHBoxLayout()
    theme_btn = QPushButton("切换到深色主题")
    refresh_btn = QPushButton("刷新corner button样式")
    button_layout.addWidget(theme_btn)
    button_layout.addWidget(refresh_btn)
    layout.addLayout(button_layout)
    
    # 创建表格
    table = create_test_table()
    layout.addWidget(QLabel("测试表格 - 注意左上角corner button的颜色:"))
    layout.addWidget(table)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("切换到深色主题")
                apply_light_theme_table_styles(table)
                main_window.setStyleSheet("")
                is_dark = False
                print("已切换到浅色主题 - corner button应该是浅色")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("切换到浅色主题")
                apply_dark_theme_table_styles(table)
                # 应用主窗口深色样式
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                        border-radius: 4px;
                    }
                    QPushButton:hover { 
                        background-color: #4A90E2; 
                        border-color: #357ABD;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                is_dark = True
                print("已切换到深色主题 - corner button应该是深色")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                apply_light_theme_table_styles(table)
                main_window.setStyleSheet("")
                theme_btn.setText("切换到深色主题")
                is_dark = False
                print("手动切换到浅色主题")
            else:
                apply_dark_theme_table_styles(table)
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                theme_btn.setText("切换到浅色主题")
                is_dark = True
                print("手动切换到深色主题")
    
    def refresh_corner_button():
        """刷新corner button样式"""
        if is_dark:
            apply_dark_theme_table_styles(table)
            print("刷新深色主题corner button样式")
        else:
            apply_light_theme_table_styles(table)
            print("刷新浅色主题corner button样式")
    
    # 连接信号
    theme_btn.clicked.connect(toggle_theme)
    refresh_btn.clicked.connect(refresh_corner_button)
    
    print("测试窗口已创建")
    print("请点击按钮测试:")
    print("1. 切换主题观察corner button颜色变化")
    print("2. 浅色主题下corner button应该是浅色 (#F5F5F5)")
    print("3. 深色主题下corner button应该是深色 (#3C3C3C)")
    print("4. 如果corner button没有变化，点击刷新按钮")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
