#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试表格文字染色问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_theme_manager():
    """测试ThemeManager"""
    print("=== 测试ThemeManager ===")
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        print(f"当前主题: {current_theme}")
        
        is_dark = theme_manager.is_dark_theme(current_theme)
        print(f"是否为深色主题: {is_dark}")
        
        # 切换到深色橙色主题
        if current_theme != 'dark_orange':
            print("切换到深色橙色主题...")
            result = theme_manager.set_theme('dark_orange')
            print(f"切换结果: {result}")
            
            new_theme = theme_manager.get_current_theme()
            print(f"切换后主题: {new_theme}")
            
            # 切换回原主题
            theme_manager.set_theme(current_theme)
            print(f"已切换回原主题: {current_theme}")
        
        return True
    except Exception as e:
        print(f"ThemeManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_colorization_settings():
    """测试ColorizationSettings"""
    print("\n=== 测试ColorizationSettings ===")
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
        
        colorization = ColorizationSettings()
        
        print("获取各种颜色:")
        print(f"关键字颜色: {colorization.get_keyword_color()}")
        print(f"变量颜色: {colorization.get_variable_color()}")
        print(f"文字颜色: {colorization.get_text_color()}")
        print(f"背景颜色: {colorization.get_background_color()}")
        print(f"注释颜色: {colorization.get_comment_color()}")
        
        return True
    except Exception as e:
        print(f"ColorizationSettings测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_colorizer():
    """测试Colorizer"""
    print("\n=== 测试Colorizer ===")
    try:
        from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
        from PyQt5.QtWidgets import QApplication, QTableWidget
        
        # 创建QApplication（如果还没有的话）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建测试表格
        table = QTableWidget(5, 5)
        
        # 创建模拟的Table对象
        class MockTable:
            def __init__(self, qt_table):
                self._table = qt_table
        
        mock_table = MockTable(table)
        
        # 创建Colorizer实例
        colorizer = Colorizer()
        
        # 测试数据
        test_lines = [
            ['Log', 'Hello World', '', '', '# 这是注释'],
            ['${variable}', '=', 'Set Variable', 'test_value', ''],
            ['FOR', '${item}', 'IN', '@{list}', ''],
            ['END', '', '', '', ''],
            ['Sleep', '1s', '', '', '']
        ]
        
        print("开始测试染色...")
        for row, line in enumerate(test_lines):
            print(f"\n染色第{row}行: {line}")
            colorizer.colorize(mock_table, row, line)
        
        print("\nColorizer测试完成")
        return True
        
    except Exception as e:
        print(f"Colorizer测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 调试表格文字染色问题...")
    print("=" * 60)
    
    # 测试各个组件
    test_theme_manager()
    test_colorization_settings()
    test_colorizer()
    
    print("\n" + "=" * 60)
    print("调试测试完成")

if __name__ == "__main__":
    main()
