#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试编辑页签背景色修复
验证深色主题和深色橙色主题下的编辑框背景色是否正确设置
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QHBoxLayout, QLabel)
from PyQt5.QtCore import Qt

def test_line_edit_area_colors():
    """测试LineEditArea的颜色设置"""
    print("=== 测试LineEditArea颜色设置 ===")
    
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试不同主题下的颜色设置
        themes_to_test = ['light', 'dark', 'dark_orange']
        
        for theme in themes_to_test:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 切换主题
            theme_manager.set_theme(theme, save_preference=False)
            current_theme = theme_manager.get_current_theme()
            print(f"当前主题: {current_theme}")
            
            # 创建LineEditArea实例
            line_edit_area = LineEditArea(None, 'Test Arguments')
            line_edit_area.load()
            
            # 测试有内容时的颜色
            line_edit_area.fill_data("test content")
            style_with_content = line_edit_area._line.styleSheet()
            print(f"有内容时样式: {style_with_content}")
            
            # 测试无内容时的颜色
            line_edit_area.fill_data("")
            style_without_content = line_edit_area._line.styleSheet()
            print(f"无内容时样式: {style_without_content}")
            
            # 验证背景色是否正确
            if theme == 'dark':
                expected_bg_with_content = "#353535"
                expected_bg_without_content = "#2A2A2A"
            elif theme == 'dark_orange':
                expected_bg_with_content = "#3A3A3A"
                expected_bg_without_content = "#2D2D2D"
            else:
                expected_bg_with_content = "#ffffff"
                expected_bg_without_content = "#c0c0c0"
            
            bg_with_content_correct = expected_bg_with_content in style_with_content
            bg_without_content_correct = expected_bg_without_content in style_without_content
            
            print(f"有内容背景色正确: {'✅' if bg_with_content_correct else '❌'}")
            print(f"无内容背景色正确: {'✅' if bg_without_content_correct else '❌'}")
            
    except Exception as e:
        print(f"测试LineEditArea时出错: {e}")
        import traceback
        traceback.print_exc()

def test_table_edit_area_colors():
    """测试TableEditArea的颜色设置"""
    print("\n=== 测试TableEditArea颜色设置 ===")
    
    try:
        from controller.system_plugin.edit.view.component.table.TableEditArea import TableEditArea
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试不同主题下的颜色设置
        themes_to_test = ['light', 'dark', 'dark_orange']
        
        for theme in themes_to_test:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 切换主题
            theme_manager.set_theme(theme, save_preference=False)
            current_theme = theme_manager.get_current_theme()
            print(f"当前主题: {current_theme}")
            
            # 创建TableEditArea实例
            table_edit_area = TableEditArea(None)
            table_edit_area.load()
            
            # 获取样式
            style = table_edit_area._table_edit_area.styleSheet()
            print(f"表格编辑区域样式: {style}")
            
            # 验证背景色是否正确
            if theme == 'dark':
                expected_bg = "#353535"
            elif theme == 'dark_orange':
                expected_bg = "#3A3A3A"
            else:
                expected_bg = "#ececec"
            
            bg_correct = expected_bg in style
            print(f"背景色正确: {'✅' if bg_correct else '❌'}")
            
    except Exception as e:
        print(f"测试TableEditArea时出错: {e}")
        import traceback
        traceback.print_exc()

def test_tag_table_colors():
    """测试Tag表格的颜色设置"""
    print("\n=== 测试Tag表格颜色设置 ===")
    
    try:
        from controller.system_plugin.edit.view.component.Tag import Tag
        from controller.system_plugin.style.ThemeManager import ThemeManager
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试不同主题下的颜色设置
        themes_to_test = ['light', 'dark', 'dark_orange']
        
        for theme in themes_to_test:
            print(f"\n--- 测试主题: {theme} ---")
            
            # 切换主题
            theme_manager.set_theme(theme, save_preference=False)
            current_theme = theme_manager.get_current_theme()
            print(f"当前主题: {current_theme}")
            
            # 创建Tag实例
            tag = Tag(None, 'Test Tags')
            tag.load()
            
            # 获取样式
            style = tag._table.styleSheet()
            print(f"Tags表格样式: {style}")
            
            # 验证背景色是否正确
            if theme == 'dark':
                expected_bg = "#353535"
            elif theme == 'dark_orange':
                expected_bg = "#3A3A3A"
            else:
                expected_bg = "border:none;"
            
            if theme in ['dark', 'dark_orange']:
                bg_correct = expected_bg in style
            else:
                bg_correct = style == expected_bg
            
            print(f"背景色正确: {'✅' if bg_correct else '❌'}")
            
    except Exception as e:
        print(f"测试Tag时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试编辑页签背景色修复...")
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    try:
        # 测试各个组件
        test_line_edit_area_colors()
        test_table_edit_area_colors()
        test_tag_table_colors()
        
        print("\n=== 测试完成 ===")
        print("如果所有测试都显示✅，说明背景色修复成功")
        print("如果有❌，请检查相应组件的样式设置")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 不启动事件循环，直接退出
    app.quit()

if __name__ == "__main__":
    main()
