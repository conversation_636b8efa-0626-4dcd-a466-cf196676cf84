#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试表格corner button和编辑区域的主题修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                             QTextEdit, QHBoxLayout)
from PyQt5.QtCore import Qt

def create_test_table_with_edit_area():
    """创建测试表格和编辑区域"""
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    # 创建编辑区域（模拟TableEditArea）
    edit_area = QTextEdit()
    edit_area.setFixedHeight(40)
    edit_area.setFontPointSize(12)
    edit_area.setPlaceholderText("表格单元格编辑区域")
    layout.addWidget(edit_area)
    
    # 创建表格
    table = QTableWidget(5, 5)
    table.setHorizontalHeaderLabels(['列1', '列2', '列3', '列4', '列5'])
    
    # 填充一些测试数据
    for row in range(5):
        for col in range(5):
            item = QTableWidgetItem(f"单元格({row},{col})")
            table.setItem(row, col, item)
    
    layout.addWidget(table)
    
    return widget, edit_area, table

def apply_light_theme_styles(edit_area, table):
    """应用浅色主题样式"""
    # 编辑区域样式
    edit_area.setStyleSheet("border: none;background-color: #ececec;color: #000000;")
    
    # 表格样式
    table_style = """
        QTableWidget {
            background-color: #FFFFFF;
            color: #000000;
            gridline-color: #D0D0D0;
            border: 1px solid #D0D0D0;
        }
        QHeaderView::section {
            background-color: #F5F5F5;
            color: #000000;
            border: 1px solid #D0D0D0;
            padding: 4px;
        }
        QTableCornerButton::section {
            background-color: #F5F5F5;
            border: 1px solid #D0D0D0;
        }
        QAbstractScrollArea::corner {
            background-color: #F5F5F5;
            border: 1px solid #D0D0D0;
        }
    """
    table.setStyleSheet(table_style)

def apply_dark_theme_styles(edit_area, table):
    """应用深色主题样式"""
    # 编辑区域样式
    edit_area_style = """
        QTextEdit {
            border: none;
            background-color: #404040;
            color: #E0E0E0;
            selection-background-color: #4A90E2;
            selection-color: #FFFFFF;
        }
    """
    edit_area.setStyleSheet(edit_area_style)
    
    # 表格样式
    table_style = """
        QTableWidget {
            background-color: #404040;
            color: #E0E0E0;
            gridline-color: #555555;
            border: 1px solid #555555;
        }
        QHeaderView::section {
            background-color: #3C3C3C;
            color: #E0E0E0;
            border: 1px solid #555555;
            padding: 4px;
        }
        QTableCornerButton::section {
            background-color: #3C3C3C;
            border: 1px solid #555555;
        }
        QAbstractScrollArea::corner {
            background-color: #3C3C3C;
            border: 1px solid #555555;
        }
    """
    table.setStyleSheet(table_style)

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("表格Corner Button和编辑区域主题测试")
    main_window.resize(800, 600)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("测试表格corner button和编辑区域的主题样式")
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建主题切换按钮
    theme_btn = QPushButton("切换到深色主题")
    layout.addWidget(theme_btn)
    
    # 创建表格和编辑区域
    table_widget, edit_area, table = create_test_table_with_edit_area()
    layout.addWidget(table_widget)
    
    # 主题切换状态
    is_dark = False
    
    def toggle_theme():
        nonlocal is_dark
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            
            if is_dark:
                theme_manager.set_theme('light')
                theme_btn.setText("切换到深色主题")
                apply_light_theme_styles(edit_area, table)
                main_window.setStyleSheet("")
                is_dark = False
                print("已切换到浅色主题")
            else:
                theme_manager.set_theme('dark')
                theme_btn.setText("切换到浅色主题")
                apply_dark_theme_styles(edit_area, table)
                # 应用主窗口深色样式
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                        border-radius: 4px;
                    }
                    QPushButton:hover { 
                        background-color: #4A90E2; 
                        border-color: #357ABD;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                is_dark = True
                print("已切换到深色主题")
                
        except Exception as e:
            print(f"主题切换失败: {e}")
            # 手动应用样式
            if is_dark:
                apply_light_theme_styles(edit_area, table)
                main_window.setStyleSheet("")
                theme_btn.setText("切换到深色主题")
                is_dark = False
                print("手动切换到浅色主题")
            else:
                apply_dark_theme_styles(edit_area, table)
                main_window.setStyleSheet("""
                    QMainWindow { background-color: #2B2B2B; color: #E0E0E0; }
                    QWidget { background-color: #2B2B2B; color: #E0E0E0; }
                    QPushButton { 
                        background-color: #404040; 
                        color: #E0E0E0; 
                        border: 1px solid #555555; 
                        padding: 6px 12px;
                    }
                    QLabel { color: #E0E0E0; }
                """)
                theme_btn.setText("切换到浅色主题")
                is_dark = True
                print("手动切换到深色主题")
    
    def on_cell_clicked():
        """模拟单元格点击事件"""
        current_item = table.currentItem()
        if current_item:
            edit_area.setPlainText(current_item.text())
            print(f"选中单元格: {current_item.text()}")
    
    def on_edit_changed():
        """模拟编辑区域文本变化"""
        current_item = table.currentItem()
        if current_item:
            current_item.setText(edit_area.toPlainText())
    
    # 连接信号
    theme_btn.clicked.connect(toggle_theme)
    table.currentItemChanged.connect(on_cell_clicked)
    edit_area.textChanged.connect(on_edit_changed)
    
    print("测试窗口已创建")
    print("请点击按钮切换主题，观察:")
    print("1. 浅色主题下表格左上角corner button是否为浅色")
    print("2. 深色主题下表格编辑区域是否为深色背景和浅色文字")
    print("3. 点击表格单元格，编辑区域应显示单元格内容")
    print("4. 在编辑区域输入文字，单元格内容应同步更新")
    
    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
