#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试光标修复
验证 "native Qt signal is not callable" 错误是否已修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer

def test_line_edit_area():
    """测试LineEditArea是否正常工作"""
    try:
        from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea

        # 创建一个模拟的父窗口
        parent = QWidget()

        # 创建LineEditArea实例
        area = LineEditArea(parent, 'Test Area')
        area.load()

        print("✅ LineEditArea创建成功")

        # 测试填充数据
        area.fill_data("测试数据")
        print("✅ 第1次数据填充成功")

        # 多次测试以确保稳定性
        for i in range(5):
            area.fill_data(f"测试数据 {i+2}")
            print(f"✅ 第{i+2}次数据填充成功")

        print("✅ 所有测试完成，没有出现 'native Qt signal is not callable' 错误")

        return area

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    app = QApplication(sys.argv)

    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("光标修复测试")
    main_window.resize(600, 400)

    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)

    # 添加说明标签
    info_label = QLabel("""
光标修复测试

问题: 出现 "native Qt signal is not callable" 错误
原因: cursor.Start 应该是 QTextCursor.Start

修复:
1. 导入 QTextCursor
2. 使用 QTextCursor.Start 替代 cursor.Start

测试: 点击按钮测试LineEditArea是否正常工作
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)

    # 创建测试按钮
    test_btn = QPushButton("测试LineEditArea")
    layout.addWidget(test_btn)

    # 创建输出文本框
    output_text = QTextEdit()
    output_text.setReadOnly(True)
    layout.addWidget(output_text)

    # 测试LineEditArea
    test_area = None

    def run_test():
        output_text.clear()
        output_text.append("=== 开始测试 ===")

        nonlocal test_area
        test_area = test_line_edit_area()

        if test_area:
            output_text.append("✅ LineEditArea创建成功")

            # 测试多次填充数据
            test_data = ["测试数据1", "测试数据2", "${variable}", "Close Browser"]
            for i, data in enumerate(test_data):
                try:
                    test_area.fill_data(data)
                    output_text.append(f"✅ 第{i+1}次数据填充成功: {data}")
                except Exception as e:
                    output_text.append(f"❌ 第{i+1}次数据填充失败: {e}")

            output_text.append("=== 测试完成 ===")
            output_text.append("如果没有出现 'native Qt signal is not callable' 错误，说明修复成功")
        else:
            output_text.append("❌ LineEditArea创建失败")

    test_btn.clicked.connect(run_test)

    # 自动运行测试
    QTimer.singleShot(1000, run_test)

    main_window.show()
    return app.exec_()

if __name__ == "__main__":
    print("🔍 光标修复测试...")
    print("=" * 50)

    # 运行控制台测试
    test_line_edit_area()

    print("\n" + "=" * 50)
    print("启动GUI测试窗口...")

    sys.exit(main())
